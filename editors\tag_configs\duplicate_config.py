"""
Duplicate tag configuration for ability editor.
Handles duplicate ability configurations with position editing.
"""

from PyQt6.QtWidgets import (QFormLayout, QSpinBox, QLabel, QVBoxLayout, QGroupBox)
from typing import Dict, Any
from .base_tag_config import BaseTagConfig


class DuplicateConfig(BaseTagConfig):
    """Configuration for duplicate tag abilities."""

    def __init__(self, editor):
        super().__init__(editor, "duplicate")

    def get_title(self) -> str:
        return "👥 Duplicate Configuration"

    def create_ui(self, parent_layout) -> None:
        """
        Create the UI widgets for duplicate configuration.

        Args:
            parent_layout: The layout to add widgets to
        """
        try:
            self.log_debug("Starting duplicate UI creation")

            # Create form layout
            form_layout = self.create_form_layout()

            # Duplicates per turn spinner (1-10)
            duplicates_per_turn = QSpinBox()
            duplicates_per_turn.setRange(1, 10)
            duplicates_per_turn.setValue(1)
            duplicates_per_turn.setToolTip("Number of duplicates that can be created per turn")
            self.store_widget("duplicates_per_turn", duplicates_per_turn)
            self.connect_change_signals(duplicates_per_turn)
            form_layout.addRow("Duplicates per Turn:", duplicates_per_turn)
            self.log_debug("Added duplicates per turn spinner")

            # Max duplicates spinner (1-20)
            max_duplicates = QSpinBox()
            max_duplicates.setRange(1, 20)
            max_duplicates.setValue(5)
            max_duplicates.setToolTip("Maximum total number of duplicates that can exist")
            self.store_widget("max_duplicates", max_duplicates)
            self.connect_change_signals(max_duplicates)
            form_layout.addRow("Max Duplicates:", max_duplicates)
            self.log_debug("Added max duplicates spinner")

            # Add to parent layout
            parent_layout.addLayout(form_layout)

            self.log_debug("Duplicate UI creation completed")

        except Exception as e:
            self.log_error(f"Error creating UI: {e}")



    def populate_data(self, data: Dict[str, Any]) -> None:
        """
        Populate the UI widgets with data from an ability.

        Args:
            data: Dictionary containing ability data
        """
        try:
            self.log_debug(f"Populating duplicate data: {data}")

            # Populate duplicates per turn
            duplicates_per_turn = self.get_widget_by_name("duplicates_per_turn")
            if duplicates_per_turn:
                per_turn_value = data.get("duplicatesPerTurn", 1)
                self.log_debug(f"Setting duplicates per turn to: {per_turn_value}")
                duplicates_per_turn.setValue(per_turn_value)

            # Populate max duplicates
            max_duplicates = self.get_widget_by_name("max_duplicates")
            if max_duplicates:
                max_value = data.get("maxDuplicates", 5)
                self.log_debug(f"Setting max duplicates to: {max_value}")
                max_duplicates.setValue(max_value)

            self.log_debug("Duplicate data populated successfully")

        except Exception as e:
            self.log_error(f"Error populating data: {e}")

    def collect_data(self) -> Dict[str, Any]:
        """
        Collect data from the UI widgets.

        Returns:
            Dictionary containing the collected data
        """
        try:
            self.log_debug("Collecting duplicate data")
            data = {}

            # Collect duplicates per turn
            duplicates_per_turn = self.get_widget_by_name("duplicates_per_turn")
            if duplicates_per_turn:
                data["duplicatesPerTurn"] = duplicates_per_turn.value()

            # Collect max duplicates
            max_duplicates = self.get_widget_by_name("max_duplicates")
            if max_duplicates:
                data["maxDuplicates"] = max_duplicates.value()

            self.log_debug(f"Collected duplicate data: {data}")
            return data

        except Exception as e:
            self.log_error(f"Error collecting data: {e}")
            return {}
