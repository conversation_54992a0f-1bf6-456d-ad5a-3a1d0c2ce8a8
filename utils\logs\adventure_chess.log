2025-07-23 13:49:15,956 - editors.ability_editor - ERROR - Failed to load tag config buff_piece_config: cannot import name 'InlinePieceSelector' from 'core.ui' (C:\Users\<USER>\Desktop\Adventure Chess  Creator- 1.1.0\core\ui\__init__.py)
2025-07-23 13:49:15,961 - editors.ability_editor - ERROR - Failed to load tag config convert_piece_config: cannot import name 'InlinePieceSelector' from 'core.ui' (C:\Users\<USER>\Desktop\Adventure Chess  Creator- 1.1.0\core\ui\__init__.py)
2025-07-23 13:49:15,964 - editors.ability_editor - ERROR - Failed to load tag config debuff_piece_config: cannot import name 'InlinePieceSelector' from 'core.ui' (C:\Users\<USER>\Desktop\Adventure Chess  Creator- 1.1.0\core\ui\__init__.py)
2025-07-23 13:49:15,968 - editors.ability_editor - ERROR - Failed to load tag config displacement_config: cannot import name 'Inline<PERSON>ieceSelector' from 'core.ui' (C:\Users\<USER>\Desktop\Adventure Chess  Creator- 1.1.0\core\ui\__init__.py)
2025-07-23 13:49:15,976 - editors.ability_editor - ERROR - Failed to load tag config immobilize_config: cannot import name 'InlinePieceSelector' from 'core.ui' (C:\Users\<USER>\Desktop\Adventure Chess  Creator- 1.1.0\core\ui\__init__.py)
2025-07-23 13:49:15,998 - editors.ability_editor - ERROR - Failed to load tag config summon_config: cannot import name 'InlinePieceSelector' from 'core.ui' (C:\Users\<USER>\Desktop\Adventure Chess  Creator- 1.1.0\core\ui\__init__.py)
2025-07-23 13:49:16,000 - editors.ability_editor - ERROR - Failed to load tag config swap_places_config: cannot import name 'InlinePieceSelector' from 'core.ui' (C:\Users\<USER>\Desktop\Adventure Chess  Creator- 1.1.0\core\ui\__init__.py)
2025-07-23 13:49:16,002 - editors.ability_editor - ERROR - Failed to load tag config trap_tile_config: cannot import name 'InlineAbilitySelector' from 'core.ui' (C:\Users\<USER>\Desktop\Adventure Chess  Creator- 1.1.0\core\ui\__init__.py)
2025-07-23 15:15:16,950 - editors.ability_editor - ERROR - Failed to load tag config buff_piece_config: cannot import name 'InlinePieceSelector' from 'core.ui' (C:\Users\<USER>\Desktop\Adventure Chess  Creator- 1.1.0\core\ui\__init__.py)
2025-07-23 15:15:16,956 - editors.ability_editor - ERROR - Failed to load tag config convert_piece_config: cannot import name 'InlinePieceSelector' from 'core.ui' (C:\Users\<USER>\Desktop\Adventure Chess  Creator- 1.1.0\core\ui\__init__.py)
2025-07-23 15:15:16,958 - editors.ability_editor - ERROR - Failed to load tag config debuff_piece_config: cannot import name 'InlinePieceSelector' from 'core.ui' (C:\Users\<USER>\Desktop\Adventure Chess  Creator- 1.1.0\core\ui\__init__.py)
2025-07-23 15:15:16,961 - editors.ability_editor - ERROR - Failed to load tag config displacement_config: cannot import name 'InlinePieceSelector' from 'core.ui' (C:\Users\<USER>\Desktop\Adventure Chess  Creator- 1.1.0\core\ui\__init__.py)
2025-07-23 15:15:16,966 - editors.ability_editor - ERROR - Failed to load tag config immobilize_config: cannot import name 'InlinePieceSelector' from 'core.ui' (C:\Users\<USER>\Desktop\Adventure Chess  Creator- 1.1.0\core\ui\__init__.py)
2025-07-23 15:15:16,982 - editors.ability_editor - ERROR - Failed to load tag config summon_config: cannot import name 'InlinePieceSelector' from 'core.ui' (C:\Users\<USER>\Desktop\Adventure Chess  Creator- 1.1.0\core\ui\__init__.py)
2025-07-23 15:15:16,983 - editors.ability_editor - ERROR - Failed to load tag config swap_places_config: cannot import name 'InlinePieceSelector' from 'core.ui' (C:\Users\<USER>\Desktop\Adventure Chess  Creator- 1.1.0\core\ui\__init__.py)
2025-07-23 15:15:16,985 - editors.ability_editor - ERROR - Failed to load tag config trap_tile_config: cannot import name 'InlineAbilitySelector' from 'core.ui' (C:\Users\<USER>\Desktop\Adventure Chess  Creator- 1.1.0\core\ui\__init__.py)
