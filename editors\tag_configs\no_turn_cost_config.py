"""
NoTurnCost tag configuration for ability editor.
Handles no turn cost ability configurations.
"""

from PyQt6.QtWidgets import Q<PERSON>pin<PERSON>ox
from typing import Dict, Any
from .base_tag_config import BaseTagConfig


class NoTurnCostConfig(BaseTagConfig):
    """Configuration for noTurnCost tag abilities."""
    
    def __init__(self, editor):
        super().__init__(editor, "noTurnCost")
    
    def create_ui(self, parent_layout) -> None:
        """
        Create the UI widgets for no turn cost configuration.

        Args:
            parent_layout: The layout to add widgets to
        """
        try:
            self.log_debug("Starting no turn cost UI creation")

            # Create form layout
            form_layout = self.create_form_layout()

            # Free uses per turn spinner (0-10, 0 = unlimited)
            free_uses_spin = QSpinBox()
            free_uses_spin.setRange(0, 10)
            free_uses_spin.setValue(1)
            free_uses_spin.setSpecialValueText("Unlimited")
            free_uses_spin.setToolTip("Number of free uses per turn (0 = unlimited)")
            self.store_widget("free_uses_spin", free_uses_spin)
            self.connect_change_signals(free_uses_spin)
            form_layout.addRow("Free Uses:", free_uses_spin)
            self.log_debug("Added free uses spinner")

            # Add to parent layout
            parent_layout.addLayout(form_layout)

            self.log_debug("No turn cost UI creation completed")

        except Exception as e:
            self.log_error(f"Error creating UI: {e}")
    
    def populate_data(self, data: Dict[str, Any]) -> None:
        """
        Populate the UI widgets with data from an ability.

        Args:
            data: Dictionary containing ability data
        """
        try:
            self.log_debug(f"Starting no turn cost data population with: {data}")

            # Populate free uses
            free_uses_spin = self.get_widget_by_name("free_uses_spin")
            if free_uses_spin:
                free_uses_value = data.get("noTurnCostFreeUses", 1)
                self.log_debug(f"Setting free uses to: {free_uses_value}")
                free_uses_spin.setValue(free_uses_value)

            self.log_debug("No turn cost data populated successfully")

        except Exception as e:
            self.log_error(f"Error populating data: {e}")
    
    def collect_data(self) -> Dict[str, Any]:
        """
        Collect data from the UI widgets.

        Returns:
            Dictionary containing the no turn cost configuration data
        """
        try:
            self.log_debug("Collecting no turn cost data")
            data = {}

            # Collect free uses
            free_uses_spin = self.get_widget_by_name("free_uses_spin")
            if free_uses_spin:
                data["noTurnCostFreeUses"] = free_uses_spin.value()

            self.log_debug(f"Collected no turn cost data: {data}")
            return data

        except Exception as e:
            self.log_error(f"Error collecting data: {e}")
            return {}
