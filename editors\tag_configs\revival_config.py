"""
Revival tag configuration for ability editor.
Handles revival-based ability configurations.
"""

from PyQt6.QtWidgets import QSpinBox, QCheckBox, QWidget
from typing import Dict, Any
from .base_tag_config import BaseTagConfig
from dialogs.inline_selectors import InlinePieceSelector


class RevivalConfig(BaseTagConfig):
    """Configuration for revival tag abilities."""
    
    def __init__(self, editor):
        super().__init__(editor, "revival")
    
    def create_ui(self, parent_layout) -> None:
        """
        Create the UI widgets for revival configuration.
        
        Args:
            parent_layout: The layout to add widgets to
        """
        try:
            self.log_debug("Starting revival UI creation")
            
            # Create form layout
            form_layout = self.create_form_layout()
            self.log_debug("Created form layout")
            
            # Revival pieces selector (InlinePieceSelector)
            revival_selector = InlinePieceSelector(
                parent=self.editor,
                title="Revival Targets",
                allow_costs=True
            )
            self.store_widget("revival_piece_selector", revival_selector)
            form_layout.addRow("Pieces to Revive:", revival_selector)
            self.log_debug("Added revival piece selector")
            
            # Maximum revivals per use spinner (1-10)
            revival_max = QSpinBox()
            revival_max.setRange(1, 10)
            revival_max.setValue(1)
            revival_max.setToolTip("Maximum number of pieces that can be revived per use")
            self.store_widget("revival_max_spinner", revival_max)
            self.connect_change_signals(revival_max)
            form_layout.addRow("Max Revivals:", revival_max)
            self.log_debug("Added revival max spinner")
            
            # Keeps points checkbox (replaces revival health)
            keeps_points = QCheckBox("Revived pieces keep their points")
            keeps_points.setChecked(True)
            keeps_points.setToolTip("Whether revived pieces retain their current point values")
            self.store_widget("revival_keeps_points", keeps_points)
            self.connect_change_signals(keeps_points)
            form_layout.addRow("", keeps_points)
            self.log_debug("Added keeps points checkbox")

            # Can self sacrifice checkbox
            can_self_sacrifice = QCheckBox("Can self sacrifice to revive")
            can_self_sacrifice.setToolTip("Whether the piece can sacrifice itself to revive others")
            self.store_widget("revival_can_self_sacrifice", can_self_sacrifice)
            self.connect_change_signals(can_self_sacrifice)
            can_self_sacrifice.toggled.connect(self.on_self_sacrifice_toggled)
            form_layout.addRow("", can_self_sacrifice)
            self.log_debug("Added can self sacrifice checkbox")

            # Self sacrifice spinner (0-99) - initially hidden
            self_sacrifice = QSpinBox()
            self_sacrifice.setRange(0, 99)
            self_sacrifice.setValue(0)
            self_sacrifice.setToolTip("Cost in points for self-sacrifice revival")
            self_sacrifice.setVisible(False)
            self.store_widget("revival_self_sacrifice", self_sacrifice)
            self.connect_change_signals(self_sacrifice)
            form_layout.addRow("Self Sacrifice Cost:", self_sacrifice)
            self.log_debug("Added self sacrifice spinner")
            
            # Can revive enemy pieces checkbox
            revive_enemies = QCheckBox("Can revive enemy pieces")
            revive_enemies.setToolTip("Whether the ability can revive enemy pieces")
            self.store_widget("revival_revive_enemies", revive_enemies)
            self.connect_change_signals(revive_enemies)
            form_layout.addRow("", revive_enemies)
            self.log_debug("Added revive enemies checkbox")
            
            # Add the form layout to the parent
            form_widget = QWidget()
            form_widget.setLayout(form_layout)
            parent_layout.addWidget(form_widget)
            
            self.log_debug("Revival UI creation completed")
            
        except Exception as e:
            self.log_error(f"Error creating UI: {e}")

    def on_self_sacrifice_toggled(self, checked):
        """Handle self sacrifice checkbox toggle."""
        try:
            self_sacrifice_spin = self.get_widget_by_name("revival_self_sacrifice")
            if self_sacrifice_spin:
                self_sacrifice_spin.setVisible(checked)
                if not checked:
                    self_sacrifice_spin.setValue(0)
            self.log_debug(f"Self sacrifice toggled: {checked}")
        except Exception as e:
            self.log_error(f"Error handling self sacrifice toggle: {e}")

    def populate_data(self, data: Dict[str, Any]) -> None:
        """
        Populate the UI widgets with data from an ability.
        
        Args:
            data: Dictionary containing ability data
        """
        try:
            self.log_debug(f"Starting revival data population with: {data}")
            
            # Populate revival pieces list
            revival_selector = self.get_widget_by_name("revival_piece_selector")
            if revival_selector:
                revival_list = data.get("revivalTargets", [])
                self.log_debug(f"Setting revival list to: {revival_list}")
                revival_selector.set_pieces(revival_list)
            
            # Populate revival max
            revival_max = self.get_widget_by_name("revival_max_spinner")
            if revival_max:
                max_value = data.get("revivalMax", 1)
                self.log_debug(f"Setting revival max to: {max_value}")
                revival_max.setValue(max_value)
            
            # Populate keeps points
            keeps_points = self.get_widget_by_name("revival_keeps_points")
            if keeps_points:
                keeps_value = data.get("revivalKeepsPoints", True)
                self.log_debug(f"Setting keeps points to: {keeps_value}")
                keeps_points.setChecked(keeps_value)

            # Populate can self sacrifice checkbox
            can_self_sacrifice = self.get_widget_by_name("revival_can_self_sacrifice")
            sacrifice_value = data.get("revivalSelfSacrifice", 0)
            has_self_sacrifice = sacrifice_value > 0
            if can_self_sacrifice:
                self.log_debug(f"Setting can self sacrifice to: {has_self_sacrifice}")
                can_self_sacrifice.setChecked(has_self_sacrifice)

            # Populate self sacrifice spinner
            self_sacrifice = self.get_widget_by_name("revival_self_sacrifice")
            if self_sacrifice:
                self.log_debug(f"Setting self sacrifice to: {sacrifice_value}")
                self_sacrifice.setValue(sacrifice_value)
                self_sacrifice.setVisible(has_self_sacrifice)
            
            # Populate revive enemies
            revive_enemies = self.get_widget_by_name("revival_revive_enemies")
            if revive_enemies:
                enemies_value = data.get("revivalReviveEnemies", False)
                self.log_debug(f"Setting revive enemies to: {enemies_value}")
                revive_enemies.setChecked(enemies_value)
            
            self.log_debug("Revival data populated successfully")
            
        except Exception as e:
            self.log_error(f"Error populating data: {e}")
    
    def collect_data(self) -> Dict[str, Any]:
        """
        Collect data from the UI widgets.
        
        Returns:
            Dictionary containing the revival configuration data
        """
        try:
            data = {}
            
            # Collect revival pieces list
            revival_selector = self.get_widget_by_name("revival_piece_selector")
            if revival_selector:
                pieces_list = revival_selector.get_pieces()
                data["revivalTargets"] = pieces_list
            
            # Collect revival max
            revival_max = self.get_widget_by_name("revival_max_spinner")
            if revival_max:
                data["revivalMax"] = revival_max.value()
            
            # Collect keeps points
            keeps_points = self.get_widget_by_name("revival_keeps_points")
            if keeps_points:
                data["revivalKeepsPoints"] = keeps_points.isChecked()

            # Collect self sacrifice (only if checkbox is checked)
            can_self_sacrifice = self.get_widget_by_name("revival_can_self_sacrifice")
            self_sacrifice = self.get_widget_by_name("revival_self_sacrifice")
            if can_self_sacrifice and can_self_sacrifice.isChecked() and self_sacrifice:
                data["revivalSelfSacrifice"] = self_sacrifice.value()
            else:
                data["revivalSelfSacrifice"] = 0
            
            # Collect revive enemies
            revive_enemies = self.get_widget_by_name("revival_revive_enemies")
            if revive_enemies:
                data["revivalReviveEnemies"] = revive_enemies.isChecked()
            
            self.log_debug(f"Collected revival data: {data}")
            return data
            
        except Exception as e:
            self.log_error(f"Error collecting data: {e}")
            return {}
