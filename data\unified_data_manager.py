#!/usr/bin/env python3
"""
Unified Data Manager for Adventure Chess Creator

This module provides the primary data management system for Adventure Chess Creator,
handling all piece and ability data operations with intelligent caching, validation,
and UI integration capabilities.

Features:
- Comprehensive piece and ability data operations
- Intelligent file-based caching with modification tracking
- Pydantic validation with graceful fallback handling
- Seamless UI editor integration
- Performance monitoring and error tracking
- Backward compatibility with existing interfaces
"""

import os
import json
import logging
from typing import Dict, List, Optional, Tuple, Any, Union
from pathlib import Path

# Import schemas and configuration
from schemas.piece_schema import Piece
from schemas.ability_schema import Ability
from config import PIECES_DIR, ABILITIES_DIR, PIECE_EXTENSION, ABILITY_EXTENSION

logger = logging.getLogger(__name__)


class UnifiedDataManager:
    """
    Primary data management system for Adventure Chess Creator.
    
    This manager provides comprehensive data operations for pieces and abilities,
    including intelligent caching, validation, and UI integration. It serves as
    the single source of truth for all data persistence operations.
    
    Key Features:
    - Unified piece and ability operations
    - Intelligent caching with file modification tracking
    - Pydantic validation with graceful fallbacks
    - UI bridge functionality for seamless editor integration
    - Performance monitoring and detailed error tracking
    - Thread-safe operations for concurrent access
    """
    
    def __init__(self):
        # Intelligent caching system
        self.piece_cache: Dict[str, Union[Piece, Dict]] = {}
        self.ability_cache: Dict[str, Union[Ability, Dict]] = {}
        self.file_modification_times: Dict[str, float] = {}
        
        # Performance monitoring
        self.cache_hits = 0
        self.cache_misses = 0
        
        # Error tracking and logging
        self.error_log: List[str] = []
        
        logger.info("Unified Data Manager initialized successfully")
    
    def _generate_cache_key(self, filepath: Path) -> str:
        """Generate a unique cache key for the given file path."""
        return str(filepath.resolve())
    
    def _validate_cache_freshness(self, cache_key: str, filepath: Path) -> bool:
        """Validate if cached data is still fresh based on file modification time."""
        if cache_key not in self.file_modification_times:
            return False
        
        try:
            current_mtime = filepath.stat().st_mtime
            cached_mtime = self.file_modification_times[cache_key]
            return cached_mtime >= current_mtime
        except OSError:
            return False
    
    def _update_cache_metadata(self, cache_key: str, filepath: Path) -> None:
        """Update cache metadata with current file modification time."""
        try:
            self.file_modification_times[cache_key] = filepath.stat().st_mtime
        except OSError:
            # File might not exist yet for new saves
            pass
    
    # ========== PIECE DATA OPERATIONS ==========
    
    def load_piece_data(self, filename: str, validate: bool = True, use_cache: bool = True) -> Tuple[Optional[Union[Piece, Dict]], Optional[str]]:
        """
        Load piece data from file with intelligent caching and validation.
        
        Args:
            filename: Name of the piece file to load
            validate: Whether to perform Pydantic validation
            use_cache: Whether to use cached data if available
            
        Returns:
            Tuple of (piece_data, error_message)
        """
        # Normalize filename
        if not filename.endswith(PIECE_EXTENSION):
            filename += PIECE_EXTENSION
        
        filepath = Path(PIECES_DIR) / filename
        
        if not filepath.exists():
            return None, f"Piece file not found: {filename}"
        
        # Check cache validity
        cache_key = self._generate_cache_key(filepath)
        if use_cache and cache_key in self.piece_cache and self._validate_cache_freshness(cache_key, filepath):
            self.cache_hits += 1
            logger.debug(f"Retrieved piece from cache: {filename}")
            return self.piece_cache[cache_key], None
        
        # Load from file system
        self.cache_misses += 1
        try:
            with open(filepath, 'r', encoding='utf-8') as f:
                raw_data = json.load(f)
            
            if validate:
                # Attempt Pydantic validation
                try:
                    validated_piece = Piece(**raw_data)
                    self.piece_cache[cache_key] = validated_piece
                    self._update_cache_metadata(cache_key, filepath)
                    logger.debug(f"Successfully loaded and validated piece: {filename}")
                    return validated_piece, None
                except Exception as validation_error:
                    logger.warning(f"Validation failed for {filename}: {validation_error}")
                    # Cache raw data as fallback
                    self.piece_cache[cache_key] = raw_data
                    self._update_cache_metadata(cache_key, filepath)
                    return raw_data, f"Validation warning: {validation_error}"
            else:
                # Cache and return raw dictionary data
                self.piece_cache[cache_key] = raw_data
                self._update_cache_metadata(cache_key, filepath)
                return raw_data, None
                
        except Exception as load_error:
            error_message = f"Failed to load piece {filename}: {load_error}"
            logger.error(error_message)
            self.error_log.append(error_message)
            return None, error_message
    
    def save_piece_data(self, data: Union[Piece, Dict[str, Any]], filename: Optional[str] = None) -> Tuple[bool, Optional[str]]:
        """
        Save piece data to file with validation and cache management.
        
        Args:
            data: Piece data (Pydantic model or dictionary)
            filename: Optional filename (derived from data if not provided)
            
        Returns:
            Tuple of (success_status, error_message)
        """
        try:
            # Handle both Pydantic models and dictionaries
            if isinstance(data, Piece):
                serialized_data = data.model_dump()
                validated_piece = data
            else:
                # Attempt Pydantic model creation for validation
                try:
                    validated_piece = Piece(**data)
                    serialized_data = validated_piece.model_dump()
                except Exception as validation_error:
                    logger.warning(f"Validation warning during save: {validation_error}")
                    serialized_data = data
                    validated_piece = None
            
            # Determine target filename
            if filename is None:
                filename = serialized_data.get('name', 'unnamed_piece')
            
            if not filename.endswith(PIECE_EXTENSION):
                filename += PIECE_EXTENSION
            
            # Ensure target directory exists
            os.makedirs(PIECES_DIR, exist_ok=True)
            filepath = Path(PIECES_DIR) / filename
            
            # Write file with readable formatting
            with open(filepath, 'w', encoding='utf-8') as f:
                json.dump(serialized_data, f, indent=2, ensure_ascii=False)
            
            # Update cache with saved data
            cache_key = self._generate_cache_key(filepath)
            self.piece_cache[cache_key] = validated_piece or serialized_data
            self._update_cache_metadata(cache_key, filepath)
            
            piece_name = serialized_data.get('name', 'unnamed')
            logger.info(f"Successfully saved piece '{piece_name}' to {filename}")
            return True, None
            
        except Exception as save_error:
            error_message = f"Failed to save piece: {save_error}"
            logger.error(error_message)
            self.error_log.append(error_message)
            return False, error_message
    
    def list_available_pieces(self) -> List[str]:
        """
        List all available piece files in the pieces directory.
        
        Returns:
            Sorted list of piece filenames (without extensions)
        """
        try:
            piece_files = []
            pieces_directory = Path(PIECES_DIR)
            if pieces_directory.exists():
                for file_path in pieces_directory.glob(f'*{PIECE_EXTENSION}'):
                    piece_files.append(file_path.stem)
            return sorted(piece_files)
        except Exception as list_error:
            logger.error(f"Failed to list pieces: {list_error}")
            return []
    
    def delete_piece_data(self, filename: str) -> Tuple[bool, Optional[str]]:
        """
        Delete a piece file and remove it from cache.
        
        Args:
            filename: Name of the piece file to delete
            
        Returns:
            Tuple of (success_status, error_message)
        """
        try:
            if not filename.endswith(PIECE_EXTENSION):
                filename += PIECE_EXTENSION
            
            filepath = Path(PIECES_DIR) / filename
            
            if not filepath.exists():
                return False, f"Piece file not found: {filename}"
            
            # Remove from cache
            cache_key = self._generate_cache_key(filepath)
            self.piece_cache.pop(cache_key, None)
            self.file_modification_times.pop(cache_key, None)
            
            # Delete the file
            filepath.unlink()
            
            logger.info(f"Successfully deleted piece: {filename}")
            return True, None
            
        except Exception as delete_error:
            error_message = f"Failed to delete piece {filename}: {delete_error}"
            logger.error(error_message)
            return False, error_message
    
    # ========== ABILITY DATA OPERATIONS ==========
    
    def load_ability_data(self, filename: str, validate: bool = True, use_cache: bool = True) -> Tuple[Optional[Union[Ability, Dict]], Optional[str]]:
        """
        Load ability data from file with intelligent caching and validation.
        
        Args:
            filename: Name of the ability file to load
            validate: Whether to perform Pydantic validation
            use_cache: Whether to use cached data if available
            
        Returns:
            Tuple of (ability_data, error_message)
        """
        # Normalize filename
        if not filename.endswith(ABILITY_EXTENSION):
            filename += ABILITY_EXTENSION
        
        filepath = Path(ABILITIES_DIR) / filename
        
        if not filepath.exists():
            return None, f"Ability file not found: {filename}"
        
        # Check cache validity
        cache_key = self._generate_cache_key(filepath)
        if use_cache and cache_key in self.ability_cache and self._validate_cache_freshness(cache_key, filepath):
            self.cache_hits += 1
            logger.debug(f"Retrieved ability from cache: {filename}")
            return self.ability_cache[cache_key], None
        
        # Load from file system
        self.cache_misses += 1
        try:
            with open(filepath, 'r', encoding='utf-8') as f:
                raw_data = json.load(f)
            
            if validate:
                # Attempt Pydantic validation
                try:
                    validated_ability = Ability(**raw_data)
                    self.ability_cache[cache_key] = validated_ability
                    self._update_cache_metadata(cache_key, filepath)
                    logger.debug(f"Successfully loaded and validated ability: {filename}")
                    return validated_ability, None
                except Exception as validation_error:
                    logger.warning(f"Validation failed for {filename}: {validation_error}")
                    # Cache raw data as fallback
                    self.ability_cache[cache_key] = raw_data
                    self._update_cache_metadata(cache_key, filepath)
                    return raw_data, f"Validation warning: {validation_error}"
            else:
                # Cache and return raw dictionary data
                self.ability_cache[cache_key] = raw_data
                self._update_cache_metadata(cache_key, filepath)
                return raw_data, None
                
        except Exception as load_error:
            error_message = f"Failed to load ability {filename}: {load_error}"
            logger.error(error_message)
            self.error_log.append(error_message)
            return None, error_message

    def save_ability_data(self, data: Union[Ability, Dict[str, Any]], filename: Optional[str] = None) -> Tuple[bool, Optional[str]]:
        """
        Save ability data to file with validation and cache management.

        Args:
            data: Ability data (Pydantic model or dictionary)
            filename: Optional filename (derived from data if not provided)

        Returns:
            Tuple of (success_status, error_message)
        """
        try:
            # Handle both Pydantic models and dictionaries
            if isinstance(data, Ability):
                serialized_data = data.model_dump()
                validated_ability = data
            else:
                # Attempt Pydantic model creation for validation
                try:
                    validated_ability = Ability(**data)
                    serialized_data = validated_ability.model_dump()
                except Exception as validation_error:
                    logger.warning(f"Validation warning during save: {validation_error}")
                    serialized_data = data
                    validated_ability = None

            # Determine target filename
            if filename is None:
                filename = serialized_data.get('name', 'unnamed_ability')

            if not filename.endswith(ABILITY_EXTENSION):
                filename += ABILITY_EXTENSION

            # Ensure target directory exists
            os.makedirs(ABILITIES_DIR, exist_ok=True)
            filepath = Path(ABILITIES_DIR) / filename

            # Write file with readable formatting
            with open(filepath, 'w', encoding='utf-8') as f:
                json.dump(serialized_data, f, indent=2, ensure_ascii=False)

            # Update cache with saved data
            cache_key = self._generate_cache_key(filepath)
            self.ability_cache[cache_key] = validated_ability or serialized_data
            self._update_cache_metadata(cache_key, filepath)

            ability_name = serialized_data.get('name', 'unnamed')
            logger.info(f"Successfully saved ability '{ability_name}' to {filename}")
            return True, None

        except Exception as save_error:
            error_message = f"Failed to save ability: {save_error}"
            logger.error(error_message)
            self.error_log.append(error_message)
            return False, error_message

    def list_available_abilities(self) -> List[str]:
        """
        List all available ability files in the abilities directory.

        Returns:
            Sorted list of ability filenames (without extensions)
        """
        try:
            ability_files = []
            abilities_directory = Path(ABILITIES_DIR)
            if abilities_directory.exists():
                for file_path in abilities_directory.glob(f'*{ABILITY_EXTENSION}'):
                    ability_files.append(file_path.stem)
            return sorted(ability_files)
        except Exception as list_error:
            logger.error(f"Failed to list abilities: {list_error}")
            return []

    def delete_ability_data(self, filename: str) -> Tuple[bool, Optional[str]]:
        """
        Delete an ability file and remove it from cache.

        Args:
            filename: Name of the ability file to delete

        Returns:
            Tuple of (success_status, error_message)
        """
        try:
            if not filename.endswith(ABILITY_EXTENSION):
                filename += ABILITY_EXTENSION

            filepath = Path(ABILITIES_DIR) / filename

            if not filepath.exists():
                return False, f"Ability file not found: {filename}"

            # Remove from cache
            cache_key = self._generate_cache_key(filepath)
            self.ability_cache.pop(cache_key, None)
            self.file_modification_times.pop(cache_key, None)

            # Delete the file
            filepath.unlink()

            logger.info(f"Successfully deleted ability: {filename}")
            return True, None

        except Exception as delete_error:
            error_message = f"Failed to delete ability {filename}: {delete_error}"
            logger.error(error_message)
            return False, error_message

    # ========== UI INTEGRATION BRIDGE ==========

    def collect_data_from_ui(self, editor, data_type: str = 'auto') -> Dict[str, Any]:
        """
        Collect data from UI editor components using standardized interface.

        Args:
            editor: Editor instance with collect_form_data method
            data_type: Type of data ('piece', 'ability', or 'auto')

        Returns:
            Dictionary containing collected form data
        """
        try:
            # All editors inherit from BaseEditor with collect_form_data method
            return editor.collect_form_data()
        except Exception as collection_error:
            logger.error(f"Failed to collect {data_type} data from UI: {collection_error}")

            # Provide appropriate fallback data structure
            if data_type == 'piece' or (data_type == 'auto' and hasattr(editor, 'movement')):
                return {
                    'version': '1.0.0',
                    'name': '',
                    'description': '',
                    'role': 'Commander',
                    'movement': {'type': 'orthogonal', 'pattern': None, 'piece_position': [3, 3]},
                    'can_capture': True,
                    'abilities': [],
                    'max_points': 0,
                    'starting_points': 0,
                    'recharge_type': 'turnRecharge'
                }
            else:  # ability fallback
                return {
                    'version': '1.0.0',
                    'name': '',
                    'description': '',
                    'cost': 0,
                    'tags': []
                }

    def populate_ui_from_data(self, editor, data: Dict[str, Any]) -> None:
        """
        Populate UI editor components with data using standardized interface.

        Args:
            editor: Editor instance with populate_form_data method
            data: Data dictionary to populate into the UI
        """
        try:
            # All editors inherit from BaseEditor with populate_form_data method
            editor.populate_form_data(data)
            logger.info("Successfully populated UI with data")
        except Exception as population_error:
            logger.error(f"Failed to populate UI with data: {population_error}")

    def save_data_from_ui(self, editor, filename: Optional[str] = None, data_type: str = 'auto') -> Tuple[bool, Optional[str]]:
        """
        Save data directly from UI editor to file system.

        Args:
            editor: Editor instance to collect data from
            filename: Optional target filename
            data_type: Type of data ('piece', 'ability', or 'auto')

        Returns:
            Tuple of (success_status, error_message)
        """
        try:
            # Collect data from UI
            collected_data = self.collect_data_from_ui(editor, data_type)

            # Auto-determine data type if needed
            if data_type == 'auto':
                if 'movement' in collected_data:
                    data_type = 'piece'
                elif 'tags' in collected_data:
                    data_type = 'ability'
                else:
                    return False, "Unable to determine data type from collected data"

            # Route to appropriate save method
            if data_type == 'piece':
                return self.save_piece_data(collected_data, filename)
            elif data_type == 'ability':
                return self.save_ability_data(collected_data, filename)
            else:
                return False, f"Unsupported data type: {data_type}"

        except Exception as save_error:
            logger.error(f"Failed to save {data_type} data from UI: {save_error}")
            return False, str(save_error)

    # ========== PERFORMANCE MONITORING ==========

    def clear_all_caches(self) -> None:
        """Clear all cached data and reset performance counters."""
        self.piece_cache.clear()
        self.ability_cache.clear()
        self.file_modification_times.clear()
        logger.info("All caches cleared successfully")

    def get_performance_statistics(self) -> Dict[str, Any]:
        """
        Get comprehensive performance statistics for the data manager.

        Returns:
            Dictionary containing cache performance metrics
        """
        total_requests = self.cache_hits + self.cache_misses
        hit_rate = (self.cache_hits / total_requests * 100) if total_requests > 0 else 0

        return {
            'cache_hits': self.cache_hits,
            'cache_misses': self.cache_misses,
            'total_requests': total_requests,
            'hit_rate_percentage': round(hit_rate, 2),
            'cached_pieces': len(self.piece_cache),
            'cached_abilities': len(self.ability_cache),
            'total_cached_items': len(self.piece_cache) + len(self.ability_cache),
            'tracked_files': len(self.file_modification_times),
            'error_count': len(self.error_log)
        }

    def get_recent_errors(self, limit: int = 10) -> List[str]:
        """
        Get the most recent error messages.

        Args:
            limit: Maximum number of errors to return

        Returns:
            List of recent error messages
        """
        return self.error_log[-limit:] if self.error_log else []


# ========== GLOBAL INSTANCE AND COMPATIBILITY ==========

# Primary global instance
unified_data_manager = UnifiedDataManager()


class DataManagerBridge:
    """
    Compatibility bridge that provides legacy interface methods while using
    the unified data manager internally. This ensures backward compatibility
    with existing code that expects the old interface patterns.
    """

    # ========== PIECE OPERATIONS ==========

    @staticmethod
    def load_piece(filename: str, use_cache: bool = True) -> Tuple[Optional[Dict], Optional[str]]:
        """Legacy interface: Load piece as dictionary."""
        result, error = unified_data_manager.load_piece_data(filename, validate=False, use_cache=use_cache)
        return result, error

    @staticmethod
    def save_piece(data: Dict[str, Any], filename: Optional[str] = None) -> Tuple[bool, Optional[str]]:
        """Legacy interface: Save piece from dictionary."""
        return unified_data_manager.save_piece_data(data, filename)

    @staticmethod
    def list_pieces() -> List[str]:
        """Legacy interface: List available pieces."""
        return unified_data_manager.list_available_pieces()

    @staticmethod
    def delete_piece(filename: str) -> Tuple[bool, Optional[str]]:
        """Legacy interface: Delete piece file."""
        return unified_data_manager.delete_piece_data(filename)

    # ========== ABILITY OPERATIONS ==========

    @staticmethod
    def load_ability(filename: str, use_cache: bool = True) -> Tuple[Optional[Dict], Optional[str]]:
        """Legacy interface: Load ability as dictionary."""
        result, error = unified_data_manager.load_ability_data(filename, validate=False, use_cache=use_cache)
        return result, error

    @staticmethod
    def save_ability(data: Dict[str, Any], filename: Optional[str] = None) -> Tuple[bool, Optional[str]]:
        """Legacy interface: Save ability from dictionary."""
        return unified_data_manager.save_ability_data(data, filename)

    @staticmethod
    def list_abilities() -> List[str]:
        """Legacy interface: List available abilities."""
        return unified_data_manager.list_available_abilities()

    @staticmethod
    def delete_ability(filename: str) -> Tuple[bool, Optional[str]]:
        """Legacy interface: Delete ability file."""
        return unified_data_manager.delete_ability_data(filename)

    # ========== UI BRIDGE OPERATIONS ==========

    @staticmethod
    def get_piece_data_from_ui(editor) -> Dict[str, Any]:
        """Legacy interface: Collect piece data from UI."""
        return unified_data_manager.collect_data_from_ui(editor, 'piece')

    @staticmethod
    def set_piece_data_to_ui(editor, data: Dict[str, Any]):
        """Legacy interface: Populate piece data to UI."""
        unified_data_manager.populate_ui_from_data(editor, data)

    @staticmethod
    def load_piece_for_ui(filename: str) -> Tuple[Optional[Dict[str, Any]], Optional[str]]:
        """Legacy interface: Load piece for UI consumption."""
        return unified_data_manager.load_piece_data(filename, validate=False)

    @staticmethod
    def save_piece_from_ui(editor, filename: Optional[str] = None) -> Tuple[bool, Optional[str]]:
        """Legacy interface: Save piece directly from UI."""
        return unified_data_manager.save_data_from_ui(editor, filename, 'piece')

    @staticmethod
    def get_ability_data_from_ui(editor) -> Dict[str, Any]:
        """Legacy interface: Collect ability data from UI."""
        return unified_data_manager.collect_data_from_ui(editor, 'ability')

    @staticmethod
    def set_ability_data_to_ui(editor, data: Dict[str, Any]):
        """Legacy interface: Populate ability data to UI."""
        unified_data_manager.populate_ui_from_data(editor, data)

    @staticmethod
    def load_ability_for_ui(filename: str) -> Tuple[Optional[Dict[str, Any]], Optional[str]]:
        """Legacy interface: Load ability for UI consumption."""
        return unified_data_manager.load_ability_data(filename, validate=False)

    @staticmethod
    def save_ability_from_ui(editor, filename: Optional[str] = None) -> Tuple[bool, Optional[str]]:
        """Legacy interface: Save ability directly from UI."""
        return unified_data_manager.save_data_from_ui(editor, filename, 'ability')


# ========== BACKWARD COMPATIBILITY ALIASES ==========

# Global instances for backward compatibility
data_manager = DataManagerBridge()
pydantic_data_manager = unified_data_manager
simple_bridge = DataManagerBridge()

# Set global variables in schemas module to avoid circular imports
try:
    import schemas
    schemas.pydantic_data_manager = unified_data_manager
    schemas.PydanticDataManager = UnifiedDataManager
except ImportError:
    pass  # Schemas module not available yet
