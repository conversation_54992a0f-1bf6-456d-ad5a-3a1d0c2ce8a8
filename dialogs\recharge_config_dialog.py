"""
Recharge Configuration Dialog for Adventure Chess Creator

This dialog handles all recharge-related configuration for pieces including:
- Points configuration (max points, starting points)
- Recharge type selection (none, every turn, adjacent, committed)
- Type-specific configuration (points per turn, adjacency, commitment turns)
"""

import logging
from typing import Dict, Any, Optional
from PyQt6.QtWidgets import (
    QWidget, QVBoxLayout, QHBoxLayout, QFormLayout,
    QLabel, QPushButton, QSpinBox, QComboBox, QGroupBox
)
from PyQt6.QtCore import Qt

from .base_dialog import BaseDialog
from .unified_adjacency_dialog import edit_adjacency_config

logger = logging.getLogger(__name__)


class RechargeConfigDialog(BaseDialog):
    """
    Dialog for configuring piece recharge settings including points and recharge types.
    
    Handles:
    - Max points and starting points
    - Recharge type selection (none, every turn, adjacent, committed)
    - Type-specific configuration
    """
    
    def __init__(self, parent=None, initial_config=None):
        # Initialize data before calling parent constructor
        self.config = initial_config.copy() if initial_config else {
            'max_points': 0,
            'starting_points': 0,
            'recharge_type': 'none',
            'turn_points': 1,
            'adjacency_config': None,
            'commitment_turns': 1
        }
        
        # Call parent constructor
        super().__init__(parent, "Recharge Configuration", (500, 400))

    def validate_data(self) -> bool:
        """Validate the current dialog data."""
        errors = []
        
        # Points validation
        max_points = self.max_points_input.value()
        starting_points = self.starting_points_input.value()
        
        if starting_points > max_points:
            errors.append("Starting points cannot exceed max points")
        
        # Recharge type specific validation
        recharge_type = self.recharge_type_combo.currentData()
        if recharge_type == 'adjacent' and not self.config.get('adjacency_config'):
            errors.append("Adjacency configuration is required for adjacent recharge type")
        
        if errors:
            self.validation_failed.emit('\n'.join(errors))
            return False
        
        return True

    def collect_data(self) -> Dict[str, Any]:
        """Collect data from the dialog."""
        return {
            'max_points': self.max_points_input.value(),
            'starting_points': self.starting_points_input.value(),
            'recharge_type': self.recharge_type_combo.currentData(),
            'turn_points': self.turn_points_input.value(),
            'adjacency_config': self.config.get('adjacency_config'),
            'commitment_turns': self.commitment_turns_input.value()
        }

    def populate_data(self, data: Dict[str, Any]) -> None:
        """Populate the dialog with data."""
        self.config.update(data)
        
        # Update UI controls
        self.max_points_input.setValue(data.get('max_points', 0))
        self.starting_points_input.setValue(data.get('starting_points', 0))
        
        # Set recharge type
        recharge_type = data.get('recharge_type', 'none')
        for i in range(self.recharge_type_combo.count()):
            if self.recharge_type_combo.itemData(i) == recharge_type:
                self.recharge_type_combo.setCurrentIndex(i)
                break
        
        self.turn_points_input.setValue(data.get('turn_points', 1))
        self.commitment_turns_input.setValue(data.get('commitment_turns', 1))
        
        # Update UI state
        self.on_recharge_type_changed()

    def setup_ui(self):
        """Setup the dialog UI."""
        # Instructions
        instructions = self.create_description_label(
            "Configure the point system and recharge behavior for this piece."
        )
        self.content_layout.addWidget(instructions)
        
        # Points Configuration Section
        points_group = self.create_section_group("Points Configuration")
        points_layout = QFormLayout()
        
        self.max_points_input = QSpinBox()
        self.max_points_input.setRange(0, 100)
        self.max_points_input.setValue(self.config.get('max_points', 0))
        points_layout.addRow("Max Points:", self.max_points_input)
        
        self.starting_points_input = QSpinBox()
        self.starting_points_input.setRange(0, 100)
        self.starting_points_input.setValue(self.config.get('starting_points', 0))
        points_layout.addRow("Starting Points:", self.starting_points_input)
        
        points_group.setLayout(points_layout)
        self.content_layout.addWidget(points_group)
        
        # Recharge Type Section
        recharge_group = self.create_section_group("Recharge Configuration")
        recharge_layout = QVBoxLayout()
        
        # Recharge type selector
        type_layout = QFormLayout()
        self.recharge_type_combo = QComboBox()
        recharge_types = [
            ('None', 'none'),
            ('Every Turn', 'every_turn'),
            ('Adjacent', 'adjacent'),
            ('Committed', 'committed')
        ]
        for display_name, value in recharge_types:
            self.recharge_type_combo.addItem(display_name, value)
        
        # Set initial value
        initial_type = self.config.get('recharge_type', 'none')
        for i in range(self.recharge_type_combo.count()):
            if self.recharge_type_combo.itemData(i) == initial_type:
                self.recharge_type_combo.setCurrentIndex(i)
                break
        
        type_layout.addRow("Recharge Type:", self.recharge_type_combo)
        recharge_layout.addLayout(type_layout)
        
        # Type-specific configuration area
        self.config_area = QWidget()
        self.config_layout = QVBoxLayout()
        self.config_area.setLayout(self.config_layout)
        recharge_layout.addWidget(self.config_area)
        
        recharge_group.setLayout(recharge_layout)
        self.content_layout.addWidget(recharge_group)
        
        # Connect signals
        self.recharge_type_combo.currentIndexChanged.connect(self.on_recharge_type_changed)
        
        # Initialize UI state
        self.on_recharge_type_changed()

    def on_recharge_type_changed(self):
        """Handle recharge type selection change."""
        # Clear existing configuration widgets
        for i in reversed(range(self.config_layout.count())):
            child = self.config_layout.itemAt(i).widget()
            if child:
                child.setParent(None)
        
        recharge_type = self.recharge_type_combo.currentData()
        
        if recharge_type == 'every_turn':
            self.setup_every_turn_config()
        elif recharge_type == 'adjacent':
            self.setup_adjacent_config()
        elif recharge_type == 'committed':
            self.setup_committed_config()

    def setup_every_turn_config(self):
        """Setup configuration for every turn recharge."""
        form_layout = QFormLayout()
        
        self.turn_points_input = QSpinBox()
        self.turn_points_input.setRange(1, 20)
        self.turn_points_input.setValue(self.config.get('turn_points', 1))
        form_layout.addRow("Points per Turn:", self.turn_points_input)
        
        widget = QWidget()
        widget.setLayout(form_layout)
        self.config_layout.addWidget(widget)

    def setup_adjacent_config(self):
        """Setup configuration for adjacency recharge."""
        layout = QVBoxLayout()
        
        # Adjacency configuration button
        self.adjacency_btn = QPushButton("Configure Adjacency")
        self.adjacency_btn.clicked.connect(self.edit_adjacency_config)
        layout.addWidget(self.adjacency_btn)
        
        # Status label
        self.adjacency_status = QLabel("No adjacency configuration")
        if self.config.get('adjacency_config'):
            pieces_count = len(self.config['adjacency_config'].get('pieces', []))
            self.adjacency_status.setText(f"Configured with {pieces_count} piece types")
        layout.addWidget(self.adjacency_status)
        
        widget = QWidget()
        widget.setLayout(layout)
        self.config_layout.addWidget(widget)

    def setup_committed_config(self):
        """Setup configuration for committed recharge."""
        form_layout = QFormLayout()
        
        self.commitment_turns_input = QSpinBox()
        self.commitment_turns_input.setRange(1, 10)
        self.commitment_turns_input.setValue(self.config.get('commitment_turns', 1))
        form_layout.addRow("Turns of Commitment:", self.commitment_turns_input)
        
        widget = QWidget()
        widget.setLayout(form_layout)
        self.config_layout.addWidget(widget)

    def edit_adjacency_config(self):
        """Open adjacency configuration dialog."""
        config = edit_adjacency_config(
            parent=self,
            initial_config=self.config.get('adjacency_config'),
            dialog_type="adjacency_recharge"
        )
        
        if config is not None:
            self.config['adjacency_config'] = config
            # Update status label
            pieces_count = len(config.get('pieces', []))
            self.adjacency_status.setText(f"Configured with {pieces_count} piece types")


def edit_recharge_config(parent=None, initial_config=None):
    """
    Convenience function to edit recharge configuration.
    
    Args:
        parent: Parent widget
        initial_config: Initial configuration dict
    
    Returns:
        Configuration dict if accepted, None if cancelled
    """
    dialog = RechargeConfigDialog(parent, initial_config)
    if dialog.exec() == dialog.DialogCode.Accepted:
        return dialog.collect_data()
    return None
