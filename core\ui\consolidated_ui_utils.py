#!/usr/bin/env python3
"""
Consolidated UI Utilities for Adventure Chess Creator

This module consolidates functionality from ui_utilities.py and ui_utils.py
into a single comprehensive utility system for UI creation and management.

Features:
- Simple UI component creation (headers, info boxes, buttons)
- Responsive design helpers and layout utilities
- Consistent styling and behavior across components
- Backward compatibility with existing imports

Usage:
    from core.ui.consolidated_ui_utils import UIUtils
    
    # Simple components
    header = UIUtils.create_section_header("Title", "Description")
    info_box = UIUtils.create_info_box("Message", "info")
    
    # Responsive components
    scroll_area = UIUtils.create_responsive_scroll_area()
    layout = UIUtils.create_responsive_layout()
"""

from typing import Tuple, Optional, List, Any
from PyQt6.QtCore import QSize, Qt
from PyQt6.QtWidgets import (
    QFrame, QGridLayout, QHBoxLayout, QScrollArea, QSizePolicy,
    QSplitter, QVBoxLayout, QWidget, <PERSON><PERSON><PERSON><PERSON>, Q<PERSON>ush<PERSON>utton
)

from config import LAYOUT_MARGIN, LAYOUT_SPACING, WIDGET_SPACING


class UIUtils:
    """
    Consolidated UI utilities combining simple components and responsive design
    """
    
    # ========== SIMPLE UI COMPONENTS ==========
    
    @staticmethod
    def create_section_header(title: str, description: str = "") -> QLabel:
        """Create a consistent section header"""
        if description:
            text = f"<h3>{title}</h3><p style='color: #666; margin-top: 5px;'>{description}</p>"
        else:
            text = f"<h3>{title}</h3>"

        label = QLabel(text)
        label.setStyleSheet(
            """
            QLabel {
                padding: 10px;
                background-color: palette(base);
                color: palette(text);
                border-left: 4px solid palette(highlight);
                margin-bottom: 10px;
            }
        """
        )
        return label

    @staticmethod
    def create_info_box(message: str, box_type: str = "info") -> QLabel:
        """Create an info box with consistent styling"""
        colors = {
            "info": {"bg": "#d1ecf1", "border": "#bee5eb", "text": "#0c5460"},
            "success": {"bg": "#d4edda", "border": "#c3e6cb", "text": "#155724"},
            "warning": {"bg": "#fff3cd", "border": "#ffeaa7", "text": "#856404"},
            "error": {"bg": "#f8d7da", "border": "#f5c6cb", "text": "#721c24"},
        }

        color_scheme = colors.get(box_type, colors["info"])
        
        label = QLabel(message)
        label.setWordWrap(True)
        label.setStyleSheet(f"""
            QLabel {{
                background-color: {color_scheme['bg']};
                border: 1px solid {color_scheme['border']};
                color: {color_scheme['text']};
                padding: 10px;
                border-radius: 4px;
                margin: 5px 0;
            }}
        """)
        return label

    @staticmethod
    def create_legend_item(color: str, label: str) -> QWidget:
        """Create a legend item for grids"""
        widget = QWidget()
        layout = QHBoxLayout(widget)
        layout.setContentsMargins(0, 0, 0, 0)
        layout.setSpacing(5)

        # Color indicator
        color_label = QLabel()
        color_label.setFixedSize(16, 16)
        color_label.setStyleSheet(f"""
            QLabel {{
                background-color: {color};
                border: 1px solid #333;
                border-radius: 2px;
            }}
        """)

        # Text label
        text_label = QLabel(label)
        text_label.setStyleSheet("color: palette(text);")

        layout.addWidget(color_label)
        layout.addWidget(text_label)
        layout.addStretch()

        return widget

    @staticmethod
    def create_dialog_buttons() -> Tuple[QPushButton, QPushButton]:
        """Create standard dialog buttons (OK, Cancel)"""
        ok_button = QPushButton("OK")
        cancel_button = QPushButton("Cancel")
        
        # Apply basic styling
        button_style = """
            QPushButton {
                padding: 6px 12px;
                min-width: 80px;
            }
        """
        ok_button.setStyleSheet(button_style)
        cancel_button.setStyleSheet(button_style)
        
        return ok_button, cancel_button

    @staticmethod
    def create_grid_instructions(instructions: List[str]) -> QLabel:
        """Create grid instruction text"""
        text = "<ul>" + "".join(f"<li>{instruction}</li>" for instruction in instructions) + "</ul>"
        
        label = QLabel(text)
        label.setWordWrap(True)
        label.setStyleSheet("""
            QLabel {
                background-color: palette(base);
                color: palette(text);
                padding: 10px;
                border: 1px solid palette(mid);
                border-radius: 4px;
                margin: 5px 0;
            }
        """)
        return label

    # ========== RESPONSIVE DESIGN HELPERS ==========

    @staticmethod
    def create_responsive_scroll_area(parent=None) -> 'ResponsiveScrollArea':
        """Create a responsive scroll area"""
        return ResponsiveScrollArea(parent)

    @staticmethod
    def create_responsive_layout(orientation='vertical') -> QVBoxLayout | QHBoxLayout:
        """Create a responsive layout with proper spacing"""
        if orientation == 'vertical':
            layout = QVBoxLayout()
        else:
            layout = QHBoxLayout()
        
        layout.setContentsMargins(LAYOUT_MARGIN, LAYOUT_MARGIN, LAYOUT_MARGIN, LAYOUT_MARGIN)
        layout.setSpacing(LAYOUT_SPACING)
        return layout

    @staticmethod
    def create_responsive_splitter(orientation=Qt.Orientation.Horizontal) -> QSplitter:
        """Create a responsive splitter"""
        splitter = QSplitter(orientation)
        splitter.setChildrenCollapsible(False)
        return splitter

    @staticmethod
    def make_widget_responsive(widget: QWidget) -> None:
        """Make a widget responsive"""
        widget.setSizePolicy(QSizePolicy.Policy.Expanding, QSizePolicy.Policy.Expanding)

    @staticmethod
    def create_scrollable_content(content_widget: QWidget) -> QScrollArea:
        """Create scrollable content wrapper"""
        scroll_area = QScrollArea()
        scroll_area.setWidgetResizable(True)
        scroll_area.setHorizontalScrollBarPolicy(Qt.ScrollBarPolicy.ScrollBarAsNeeded)
        scroll_area.setVerticalScrollBarPolicy(Qt.ScrollBarPolicy.ScrollBarAsNeeded)
        scroll_area.setFrameStyle(QFrame.Shape.NoFrame)
        scroll_area.setWidget(content_widget)
        return scroll_area

    @staticmethod
    def optimize_layout_for_small_screens(layout: QVBoxLayout | QHBoxLayout) -> None:
        """Optimize layout for small screens"""
        layout.setSpacing(max(1, LAYOUT_SPACING // 2))
        layout.setContentsMargins(
            max(1, LAYOUT_MARGIN // 2),
            max(1, LAYOUT_MARGIN // 2),
            max(1, LAYOUT_MARGIN // 2),
            max(1, LAYOUT_MARGIN // 2)
        )


class ResponsiveScrollArea(QScrollArea):
    """Scroll area that adapts to content and window size with zoom support"""

    def __init__(self, parent=None):
        super().__init__(parent)
        self.setWidgetResizable(True)
        self.setHorizontalScrollBarPolicy(Qt.ScrollBarPolicy.ScrollBarAsNeeded)
        self.setVerticalScrollBarPolicy(Qt.ScrollBarPolicy.ScrollBarAsNeeded)
        self.setFrameStyle(QFrame.Shape.NoFrame)

        # Zoom functionality
        self.zoom_factor = 1.0
        self.min_zoom = 0.5
        self.max_zoom = 2.0

        # Create content widget with proper sizing
        self.content_widget = QWidget()
        self.content_widget.setSizePolicy(
            QSizePolicy.Policy.Expanding, QSizePolicy.Policy.Expanding
        )

        self.content_layout = QVBoxLayout(self.content_widget)
        self.content_layout.setContentsMargins(
            LAYOUT_MARGIN, LAYOUT_MARGIN, LAYOUT_MARGIN, LAYOUT_MARGIN
        )
        self.content_layout.setSpacing(LAYOUT_SPACING)

        self.setWidget(self.content_widget)

    def add_content(self, widget: QWidget) -> None:
        """Add content to the scroll area"""
        self.content_layout.addWidget(widget)

    def set_zoom(self, factor: float) -> None:
        """Set zoom factor for content"""
        factor = max(self.min_zoom, min(self.max_zoom, factor))
        self.zoom_factor = factor
        
        # Apply zoom to content widget
        if self.content_widget:
            self.content_widget.setStyleSheet(f"QWidget {{ font-size: {int(12 * factor)}px; }}")

    def zoom_in(self) -> None:
        """Zoom in by 10%"""
        self.set_zoom(self.zoom_factor * 1.1)

    def zoom_out(self) -> None:
        """Zoom out by 10%"""
        self.set_zoom(self.zoom_factor / 1.1)

    def reset_zoom(self) -> None:
        """Reset zoom to 100%"""
        self.set_zoom(1.0)


# ========== BACKWARD COMPATIBILITY ALIASES ==========

# For ui_utilities.py compatibility
create_section_header = UIUtils.create_section_header
create_info_box = UIUtils.create_info_box
create_legend_item = UIUtils.create_legend_item
create_dialog_buttons = UIUtils.create_dialog_buttons
create_grid_instructions = UIUtils.create_grid_instructions

# For ui_utils.py compatibility
create_responsive_scroll_area = UIUtils.create_responsive_scroll_area
create_responsive_layout = UIUtils.create_responsive_layout
create_responsive_splitter = UIUtils.create_responsive_splitter
make_widget_responsive = UIUtils.make_widget_responsive
create_scrollable_content = UIUtils.create_scrollable_content
optimize_layout_for_small_screens = UIUtils.optimize_layout_for_small_screens

# Legacy class aliases
UIUtilities = UIUtils  # For ui_utilities.py compatibility
