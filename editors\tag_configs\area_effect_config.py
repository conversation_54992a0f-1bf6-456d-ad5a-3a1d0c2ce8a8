"""
Area Effect tag configuration for ability editor.
Handles area-of-effect ability configurations matching old editor exactly.
"""

from PyQt6.QtWidgets import (
    QSpinBox, QComboBox, QLabel,
    QVBoxLayout, QFormLayout
)
from PyQt6.QtCore import Qt
from typing import Dict, Any
from .base_tag_config import BaseTagConfig


class AreaEffectConfig(BaseTagConfig):
    """Configuration for areaEffect tag abilities matching old editor exactly."""

    def __init__(self, editor):
        super().__init__(editor, "areaEffect")
        # Initialize area effect data structures
        self.area_effect_target = [4, 4]  # Where ability targets
        self.area_effect_center = [4, 4]  # Center of effect area
        self.custom_area_pattern = [[False for _ in range(8)] for _ in range(8)]  # 8x8 boolean grid
        self.area_effect_grid = []

    def create_ui(self, parent_layout) -> None:
        """
        Create the UI widgets for area effect configuration matching old editor exactly.

        Args:
            parent_layout: The layout to add widgets to
        """
        try:
            self.log_debug("Starting area effect UI creation")

            # Main layout
            layout = QVBoxLayout()

            # Controls
            controls_layout = QFormLayout()

            # Effect size spinner (1-8 to match old editor exactly)
            area_size = QSpinBox()
            area_size.setRange(1, 8)
            area_size.setValue(1)
            area_size.setToolTip("Size of the area effect (1=1x1, 2=2x2, etc.)")
            area_size.valueChanged.connect(self.update_area_effect_preview)
            self.store_widget("area_size", area_size)
            self.connect_change_signals(area_size)
            controls_layout.addRow("Effect Size:", area_size)
            self.log_debug("Added area size spinner")

            # Effect shape combo (exact items from old editor)
            area_shape = QComboBox()
            area_shape.addItems(["Circle", "Square", "Cross", "Line", "Custom"])
            area_shape.setToolTip("Shape of the area effect")
            area_shape.currentTextChanged.connect(self.update_area_effect_preview)
            area_shape.currentTextChanged.connect(self.on_area_shape_changed)
            self.store_widget("area_shape", area_shape)
            self.connect_change_signals(area_shape)
            controls_layout.addRow("Effect Shape:", area_shape)
            self.log_debug("Added area shape dropdown")

            layout.addLayout(controls_layout)

            # Area Effect Pattern Editor Button
            pattern_button_layout = QHBoxLayout()
            pattern_label = QLabel("Area Effect Pattern:")
            pattern_button_layout.addWidget(pattern_label)

            edit_pattern_btn = QPushButton("Edit Pattern...")
            edit_pattern_btn.setToolTip("Open range editor to configure area effect pattern")
            edit_pattern_btn.clicked.connect(self.edit_area_pattern)
            self.store_widget("edit_pattern_btn", edit_pattern_btn)
            pattern_button_layout.addWidget(edit_pattern_btn)

            pattern_button_layout.addStretch()
            layout.addLayout(pattern_button_layout)

            # Add to parent layout
            parent_layout.addLayout(layout)

            # Initialize the range editor with default pattern
            self.area_range_editor.set_pattern(self.custom_area_pattern)

            self.log_debug("Area effect UI creation completed")

        except Exception as e:
            self.log_error(f"Error creating UI: {e}")

    def edit_area_pattern(self):
        """Open the range editor dialog to configure area effect pattern."""
        try:
            from dialogs.range_editor_dialog import RangeEditorDialog

            # Convert boolean pattern to integer pattern for dialog
            int_pattern = []
            for row in self.custom_area_pattern:
                int_row = []
                for cell in row:
                    int_row.append(1 if cell else 0)
                int_pattern.append(int_row)

            dialog = RangeEditorDialog(
                parent=self.editor,
                initial_pattern=int_pattern,
                piece_pos=self.area_effect_center,
                include_starting_square=False,
                friendly_only=False
            )

            if dialog.exec() == dialog.DialogCode.Accepted:
                # Convert back to boolean pattern
                result_pattern = dialog.get_pattern()
                self.custom_area_pattern = [[bool(cell) for cell in row] for row in result_pattern]
                self.area_effect_center = dialog.get_piece_position()
                self.log_debug("Area effect pattern updated from range editor")

                # Mark as changed
                if hasattr(self.editor, 'mark_unsaved_changes'):
                    self.editor.mark_unsaved_changes()

        except Exception as e:
            self.log_error(f"Error opening area pattern editor: {e}")

    def on_area_shape_changed(self, shape):
        """Handle area shape change - enable/disable size spinner for custom shapes."""
        try:
            area_size = self.get_widget_by_name("area_size")
            if area_size:
                area_size.setEnabled(shape != "Custom")

            self.log_debug(f"Area shape changed to: {shape}")
        except Exception as e:
            self.log_error(f"Error handling shape change: {e}")

    def handle_target_selection(self, row, col):
        """Handle target selection for area effect."""
        try:
            from PyQt6.QtWidgets import QApplication
            modifiers = QApplication.keyboardModifiers()

            if modifiers & Qt.KeyboardModifier.ShiftModifier:
                # Shift+Right-click: Move effect center
                self.area_effect_center = [row, col]
                self.log_debug(f"Effect center moved to: {row}, {col}")
            else:
                # Right-click: Move target
                self.area_effect_target = [row, col]
                self.log_debug(f"Target moved to: {row}, {col}")

            self.update_area_effect_preview()

            # Mark as changed
            if hasattr(self.editor, 'mark_unsaved_changes'):
                self.editor.mark_unsaved_changes()

        except Exception as e:
            self.log_error(f"Error handling target selection: {e}")



    def update_area_effect_preview(self):
        """Update the area effect pattern in the range editor."""
        try:
            area_size = self.get_widget_by_name("area_size")
            area_shape = self.get_widget_by_name("area_shape")

            if not area_size or not area_shape:
                return

            size = area_size.value()  # 1=1x1, 2=2x2, etc.
            shape = area_shape.currentText()

            # Generate pattern based on shape and size
            pattern = [[False for _ in range(8)] for _ in range(8)]
            center_r, center_c = self.area_effect_center

            if shape == "Custom":
                # Use the custom pattern
                pattern = [row[:] for row in self.custom_area_pattern]
            else:
                # Generate pattern based on shape and size
                if shape == "Square":
                    # Square pattern: size x size area centered on effect center
                    half_size = size // 2
                    for r in range(max(0, center_r - half_size), min(8, center_r + size - half_size)):
                        for c in range(max(0, center_c - half_size), min(8, center_c + size - half_size)):
                            pattern[r][c] = True

                elif shape == "Circle":
                    # Circular pattern centered on effect center
                    for r in range(8):
                        for c in range(8):
                            distance = max(abs(r - center_r), abs(c - center_c))
                            if distance <= size:
                                pattern[r][c] = True

                elif shape == "Cross":
                    # Cross pattern centered on effect center
                    for r in range(8):
                        for c in range(8):
                            if r == center_r or c == center_c:
                                distance = max(abs(r - center_r), abs(c - center_c))
                                if distance <= size:
                                    pattern[r][c] = True

                elif shape == "Line":
                    # Line pattern (horizontal line)
                    for c in range(max(0, center_c - size), min(8, center_c + size + 1)):
                        if 0 <= center_r < 8:
                            pattern[center_r][c] = True

            # Update the range editor with the new pattern
            if hasattr(self, 'area_range_editor'):
                self.area_range_editor.set_pattern_from_array(pattern)
                self.custom_area_pattern = pattern


        except Exception as e:
            self.log_error(f"Error updating area effect preview: {e}")



    def populate_data(self, data: Dict[str, Any]) -> None:
        """
        Populate the UI widgets with data from an ability.

        Args:
            data: Dictionary containing ability data
        """
        try:
            self.log_debug(f"Starting area effect data population with: {data}")

            # Populate area size
            area_size = self.get_widget_by_name("area_size")
            if area_size:
                size_value = data.get("areaEffectSize", 1)
                self.log_debug(f"Setting area size to: {size_value}")
                area_size.setValue(size_value)

            # Populate area shape
            area_shape = self.get_widget_by_name("area_shape")
            if area_shape:
                shape_value = data.get("areaEffectShape", "Circle")
                self.log_debug(f"Setting area shape to: {shape_value}")
                index = area_shape.findText(shape_value)
                if index >= 0:
                    area_shape.setCurrentIndex(index)

            # Load position data
            if 'areaEffectTarget' in data:
                self.area_effect_target = data['areaEffectTarget'][:]  # Copy
            else:
                self.area_effect_target = [4, 4]  # Default

            if 'areaEffectCenter' in data:
                self.area_effect_center = data['areaEffectCenter'][:]  # Copy
            else:
                self.area_effect_center = [4, 4]  # Default

            if 'customAreaPattern' in data and data['customAreaPattern']:
                self.custom_area_pattern = data['customAreaPattern']
            else:
                # Always ensure custom_area_pattern is a valid 8x8 boolean grid
                self.custom_area_pattern = [[False for _ in range(8)] for _ in range(8)]

            # Pattern is now stored in self.custom_area_pattern and edited via dialog

            # Update preview after loading data
            self.update_area_effect_preview()

            self.log_debug("Area effect data populated successfully")

        except Exception as e:
            self.log_error(f"Error populating data: {e}")

    def collect_data(self) -> Dict[str, Any]:
        """
        Collect data from the UI widgets.

        Returns:
            Dictionary containing the area effect configuration data
        """
        try:
            data = {}

            # Collect area size
            area_size = self.get_widget_by_name("area_size")
            if area_size:
                data["areaEffectSize"] = area_size.value()

            # Collect area shape
            area_shape = self.get_widget_by_name("area_shape")
            if area_shape:
                data["areaEffectShape"] = area_shape.currentText()

            # Collect position data
            data["areaEffectTarget"] = self.area_effect_target[:]  # Copy
            data["areaEffectCenter"] = self.area_effect_center[:]  # Copy

            # Collect pattern from stored data
            if self.custom_area_pattern:
                data["customAreaPattern"] = self.custom_area_pattern

            self.log_debug(f"Collected area effect data: {data}")
            return data

        except Exception as e:
            self.log_error(f"Error collecting data: {e}")
            return {}
