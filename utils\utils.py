"""
Utility functions for Adventure Chess
Common functions used across multiple modules
Note: File I/O operations have been moved to dedicated manager modules
"""
import os
import json
import logging
from typing import Dict, List, Optional, Tuple

# Use standard Python error handling (removed over-engineered error system)
# from core.error_handling import (
#     data_load_handler, data_save_handler, file_handler,
#     error_handler, ErrorSeverity, ErrorCategory
# )

logger = logging.getLogger(__name__)

def load_json_file(filepath: str) -> Tu<PERSON>[Optional[Dict], Optional[str]]:
    """
    Load a JSON file safely using standard Python error handling
    Returns: (data, error_message)
    """
    try:
        if not os.path.exists(filepath):
            return None, f"File not found: {filepath}"

        with open(filepath, 'r', encoding='utf-8') as f:
            data = json.load(f)
        logger.debug(f"Loaded JSON file: {filepath}")
        return data, None
    except Exception as e:
        error_msg = f"Error loading JSON file {filepath}: {e}"
        logger.error(error_msg)
        return None, error_msg

def save_json_file(filepath: str, data: Dict) -> Optional[str]:
    """
    Save data to a JSON file safely using standard Python error handling
    Returns: error_message (None if successful)
    """
    try:
        # Ensure directory exists
        os.makedirs(os.path.dirname(filepath), exist_ok=True)

        with open(filepath, 'w', encoding='utf-8') as f:
            json.dump(data, f, indent=2, ensure_ascii=False)
        logger.debug(f"Saved JSON file: {filepath}")
        return None
    except Exception as e:
        error_msg = f"Error saving JSON file {filepath}: {e}"
        logger.error(error_msg)
        return error_msg

def sanitize_filename(filename: str) -> str:
    """Sanitize a filename by removing invalid characters"""
    invalid_chars = '<>:"/\\|?*'
    sanitized = filename
    for char in invalid_chars:
        sanitized = sanitized.replace(char, '_')
    
    # Remove multiple consecutive underscores and strip
    while '__' in sanitized:
        sanitized = sanitized.replace('__', '_')
    
    sanitized = sanitized.strip('_').strip()
    
    # Ensure filename is not empty
    if not sanitized:
        sanitized = 'unnamed_file'
    
    logger.debug(f"Sanitized filename: '{filename}' -> '{sanitized}'")
    return sanitized

def get_combined_piece_target_options() -> List[str]:
    """
    Get combined piece/target options for dropdowns
    Note: This function provides fallback options to avoid import cycles
    """
    options = []

    # Add targeting options first
    options.extend(["Friendly", "Enemy", "Any"])

    # Add separator (visual only)
    options.append("--- Specific Pieces ---")

    # Add default piece names (to avoid import cycle)
    # Actual piece names should be loaded by the calling component
    options.extend(["Pawn", "Knight", "Bishop", "Rook", "Queen", "King"])

    return options

def format_validation_errors(errors: List[str], warnings: List[str] = None) -> str:
    """Format validation errors and warnings for display"""
    if not errors and not warnings:
        return "✅ Validation passed"
    
    lines = []
    
    if errors:
        lines.append(f"❌ {len(errors)} error(s):")
        for error in errors:
            lines.append(f"  • {error}")
    
    if warnings:
        lines.append(f"⚠️  {len(warnings)} warning(s):")
        for warning in warnings:
            lines.append(f"  • {warning}")
    
    return "\n".join(lines)

def setup_logging(log_level: str = "INFO") -> None:
    """Setup logging configuration for the application"""
    import logging.handlers
    
    # Create logs directory if it doesn't exist
    log_dir = os.path.join(os.path.dirname(__file__), 'logs')
    os.makedirs(log_dir, exist_ok=True)
    
    # Configure logging
    log_format = '%(asctime)s - %(name)s - %(levelname)s - %(message)s'
    
    # File handler with rotation
    file_handler = logging.handlers.RotatingFileHandler(
        os.path.join(log_dir, 'adventure_chess.log'),
        maxBytes=1024*1024,  # 1MB
        backupCount=5
    )
    file_handler.setFormatter(logging.Formatter(log_format))
    
    # Console handler
    console_handler = logging.StreamHandler()
    console_handler.setFormatter(logging.Formatter('%(name)s - %(levelname)s - %(message)s'))
    
    # Configure root logger
    root_logger = logging.getLogger()
    root_logger.setLevel(getattr(logging, log_level.upper()))
    root_logger.addHandler(file_handler)
    root_logger.addHandler(console_handler)
    
    logger.info(f"Logging initialized with level: {log_level}")

def get_app_version() -> str:
    """Get application version"""
    return "1.0.0"

def get_schema_versions() -> Dict[str, str]:
    """Get current schema versions"""
    # Use hardcoded versions since constants were moved to managers
    return {
        'piece': "1.0.0",
        'ability': "1.0.0"
    }