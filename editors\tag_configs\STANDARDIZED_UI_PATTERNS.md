# Standardized UI Patterns for Tag Configurations

This document outlines the standardized UI patterns and styling conventions for Adventure Chess Creator tag configurations.

## Overview

All tag configurations should inherit from `BaseTagConfig` and use the standardized UI creation methods to ensure consistency across the application.

## Core Principles

1. **Consistency**: All configurations should follow the same visual and interaction patterns
2. **Accessibility**: Clear labels, tooltips, and logical tab order
3. **Maintainability**: Use standardized methods rather than custom implementations
4. **Responsiveness**: Layouts should adapt to different window sizes
5. **User Experience**: Intuitive grouping and progressive disclosure

## Standardized Layout Structure

### 1. Main Layout Pattern
```python
def create_ui(self, parent_layout) -> None:
    """Create the UI using standardized patterns."""
    try:
        # Create main layout with description
        main_layout = self.create_standard_layout(
            parent_layout, 
            "Brief description of what this tag does."
        )
        
        # Add sections using standardized methods
        # ... sections here ...
        
    except Exception as e:
        self.log_error(f"Error creating UI: {e}")
```

### 2. Section Organization
- **Basic Settings**: Core configuration options (always first)
- **Advanced Options**: Optional or complex settings
- **Target Selection**: Piece selectors and targeting options
- **Pattern Configuration**: Range/area pattern editors
- **Action Buttons**: Reset, test, or other action buttons (always last)

## Standardized Widget Creation

### Form Controls
```python
# Form section with title
form_layout = self.create_form_section(main_layout, "Section Title")

# Standard spinner
spinner = self.create_standard_spinner(min_val, max_val, default_val, suffix, tooltip)
self.add_widget_with_signals("widget_name", spinner)
form_layout.addRow("Label:", spinner)

# Standard combo box
combo = self.create_standard_combo(items_list, tooltip, default_index)
self.add_widget_with_signals("widget_name", combo)
form_layout.addRow("Label:", combo)
```

### Checkboxes and Options
```python
# Vertical section for checkboxes
options_layout = self.create_vertical_section(main_layout, "Options")

# Standard checkbox
checkbox = self.create_standard_checkbox(text, tooltip, checked)
self.add_widget_with_signals("widget_name", checkbox)
options_layout.addWidget(checkbox)
```

### Specialized Widgets
```python
# Inline piece selector
selector = self.create_inline_piece_selector("Label", allow_costs=True)
if selector:
    self.add_widget_with_signals("widget_name", selector, auto_connect=False)
    layout.addWidget(selector)

# Range editor button
button = self.create_range_editor_button("Edit Pattern...", "callback_method")
layout.addWidget(button)
```

### Action Buttons
```python
# Horizontal section for buttons
button_layout = self.create_horizontal_section(main_layout)

# Standard buttons with different styles
reset_btn = self.create_standard_button("Reset", "tooltip", "secondary")
test_btn = self.create_standard_button("Test", "tooltip", "primary")
danger_btn = self.create_standard_button("Delete", "tooltip", "danger")

button_layout.addWidget(reset_btn)
button_layout.addStretch()  # Push buttons apart
button_layout.addWidget(test_btn)
```

## Widget Naming Conventions

### Consistent Naming Pattern
- Use descriptive names: `duration_spinner`, `effect_type_combo`, `persistent_checkbox`
- Include widget type in name for clarity
- Use snake_case for widget names
- Prefix with tag name if needed: `invisible_reveal_combo`

### Widget Storage
```python
# Store widget with automatic signal connection
self.add_widget_with_signals("widget_name", widget)

# Store widget without signal connection (for complex widgets)
self.add_widget_with_signals("widget_name", widget, auto_connect=False)

# Manual storage and connection (legacy compatibility)
self.store_widget("widget_name", widget)
self.connect_change_signals(widget)
```

## Data Handling Patterns

### Populate Data Method
```python
def populate_data(self, data: Dict[str, Any]) -> None:
    """Populate the UI widgets with data."""
    try:
        self.log_debug(f"Populating data: {data}")
        
        # Spinner widgets
        spinner = self.get_widget_by_name("duration_spinner")
        if spinner:
            spinner.setValue(data.get("duration", default_value))
        
        # Combo box widgets
        combo = self.get_widget_by_name("effect_type_combo")
        if combo:
            text = data.get("effectType", "Default")
            index = combo.findText(text)
            if index >= 0:
                combo.setCurrentIndex(index)
        
        # Checkbox widgets
        checkbox = self.get_widget_by_name("persistent_checkbox")
        if checkbox:
            checkbox.setChecked(data.get("persistent", False))
        
        # Complex widgets with their own populate methods
        selector = self.get_widget_by_name("target_selector")
        if selector and hasattr(selector, 'populate_data'):
            selector.populate_data(data.get("targets", []))
        
        self.log_debug("Data populated successfully")
        
    except Exception as e:
        self.log_error(f"Error populating data: {e}")
```

### Collect Data Method
```python
def collect_data(self) -> Dict[str, Any]:
    """Collect data from the UI widgets."""
    try:
        data = {}
        
        # Collect from standard widgets
        spinner = self.get_widget_by_name("duration_spinner")
        if spinner:
            data["duration"] = spinner.value()
        
        combo = self.get_widget_by_name("effect_type_combo")
        if combo:
            data["effectType"] = combo.currentText()
        
        checkbox = self.get_widget_by_name("persistent_checkbox")
        if checkbox:
            data["persistent"] = checkbox.isChecked()
        
        # Collect from complex widgets
        selector = self.get_widget_by_name("target_selector")
        if selector and hasattr(selector, 'collect_data'):
            data["targets"] = selector.collect_data()
        
        self.log_debug(f"Data collected: {data}")
        return data
        
    except Exception as e:
        self.log_error(f"Error collecting data: {e}")
        return {}
```

## Styling Guidelines

### Visual Hierarchy
1. **Main Group**: Bold title with border and padding
2. **Section Groups**: Lighter border, smaller title
3. **Form Labels**: Standard weight, aligned consistently
4. **Descriptions**: Italic, muted color, word-wrapped

### Color Scheme
- **Primary Actions**: Blue (#007acc)
- **Secondary Actions**: Light gray (#f0f0f0)
- **Danger Actions**: Red (#dc3545)
- **Muted Text**: Gray (#666)
- **Borders**: Light gray (#cccccc, #aaaaaa)

### Spacing and Layout
- **Section Padding**: 10px top, 5px sides
- **Widget Spacing**: 5px between related widgets
- **Group Margins**: 1ex top margin for visual separation
- **Button Padding**: 8px vertical, 16px horizontal

## Error Handling

### Consistent Error Patterns
```python
try:
    # UI creation or data handling code
    self.log_debug("Operation completed successfully")
    
except Exception as e:
    self.log_error(f"Error in operation: {e}")
    # Graceful degradation - don't crash the UI
```

### Validation
- Validate data ranges in spinners
- Provide meaningful tooltips
- Use appropriate widget constraints
- Handle missing or invalid data gracefully

## Migration Guide

### Converting Existing Configurations
1. Replace manual QGroupBox creation with `create_main_group()`
2. Replace manual QFormLayout with `create_form_section()`
3. Replace manual widget creation with standardized methods
4. Update widget storage to use `add_widget_with_signals()`
5. Ensure consistent naming conventions
6. Add proper error handling and logging

### Testing Checklist
- [ ] UI creates without errors
- [ ] All widgets are properly stored and accessible
- [ ] Data population works correctly
- [ ] Data collection returns expected format
- [ ] Change signals trigger unsaved changes marker
- [ ] Tooltips are informative and helpful
- [ ] Layout adapts to different window sizes
- [ ] Styling is consistent with other configurations

## Examples

See `example_standardized_config.py` for a complete implementation demonstrating all standardized patterns.
