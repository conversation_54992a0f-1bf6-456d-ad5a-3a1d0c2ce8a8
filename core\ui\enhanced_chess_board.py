#!/usr/bin/env python3
"""
Enhanced Chess Board Widget for Adventure Chess Creator

This module provides a comprehensive chess board widget that combines the core chess board
logic with UI framework elements, but leverages dialog base patterns to eliminate redundancy.

Replaces the oversized centralized_board.py with a more maintainable structure.
"""

from PyQt6.QtWidgets import (
    QW<PERSON>t, QVBoxLayout, QHBoxLayout, QFrame, QLabel, QPushButton, 
    QCheckBox, QButtonGroup, QGroupBox
)
from PyQt6.QtCore import Qt, pyqtSignal
from typing import Optional, List
import logging

from .chess_board_core import ChessBoardCore, BoardMode, TileState

# Import theming from base dialog patterns
try:
    from dialogs.base_dialog import BaseDialog
    from core.ui import (
        apply_theme_to_widget,
        create_themed_button,
        create_themed_label,
        create_themed_group_box
    )
    THEMING_AVAILABLE = True
except ImportError:
    THEMING_AVAILABLE = False

logger = logging.getLogger(__name__)


class EnhancedChessBoardWidget(QWidget):
    """
    Enhanced chess board widget that combines core board logic with UI controls.
    
    This widget provides a complete chess board interface with preset patterns,
    controls, and theming, while leveraging the dialog base patterns to avoid
    redundancy with the dialog framework.
    
    Features:
    - Core chess board functionality via ChessBoardCore
    - Preset pattern buttons (rook, bishop, queen, knight, king, global)
    - Mode-specific controls and paint modes
    - Consistent theming using dialog base patterns
    - Configurable UI elements (presets, controls, info display)
    """
    
    # Forward signals from core board
    pattern_changed = pyqtSignal(list)
    piece_position_changed = pyqtSignal(list)
    tile_clicked = pyqtSignal(int, int, int)
    preset_selected = pyqtSignal(str)
    
    def __init__(self, parent: Optional[QWidget] = None, mode: str = BoardMode.PATTERN,
                 title: str = "Board Editor", show_presets: bool = True,
                 show_controls: bool = True, tile_size: int = 50):
        """
        Initialize the enhanced chess board widget.
        
        Args:
            parent: Parent widget
            mode: Board mode (see BoardMode class for options)
            title: Title for the board section
            show_presets: Whether to show preset pattern buttons
            show_controls: Whether to show control checkboxes
            tile_size: Size of each tile in pixels
        """
        super().__init__(parent)
        
        # Configuration
        self.mode = mode
        self.title = title
        self.show_presets = show_presets
        self.show_controls = show_controls
        self.tile_size = tile_size
        
        # UI components
        self.chess_board_core: Optional[ChessBoardCore] = None
        self.preset_buttons: List[QPushButton] = []
        self.preset_button_group: Optional[QButtonGroup] = None
        self.info_label: Optional[QLabel] = None
        self.controls_frame: Optional[QFrame] = None
        
        # Paint mode for pattern editing
        self.current_paint_mode = TileState.MOVE
        
        self.setup_ui()
        self.apply_theming()
    
    def setup_ui(self) -> None:
        """Setup the user interface using dialog base patterns."""
        main_layout = QVBoxLayout()
        main_layout.setSpacing(12)
        main_layout.setContentsMargins(10, 10, 10, 10)
        
        # Title
        if self.title:
            title_label = self.create_title_label()
            main_layout.addWidget(title_label)
        
        # Core chess board
        self.chess_board_core = ChessBoardCore(self, self.mode, self.tile_size)
        
        # Connect core board signals
        self.chess_board_core.pattern_changed.connect(self.pattern_changed.emit)
        self.chess_board_core.piece_position_changed.connect(self.piece_position_changed.emit)
        self.chess_board_core.tile_clicked.connect(self.tile_clicked.emit)
        self.chess_board_core.tile_clicked.connect(self.update_info_display)
        
        # Create board frame with theming
        board_frame = self.create_board_frame()
        main_layout.addWidget(board_frame)
        
        # Preset controls
        if self.show_presets:
            preset_frame = self.create_preset_controls()
            main_layout.addWidget(preset_frame)
        
        # Mode-specific controls
        if self.show_controls:
            controls_frame = self.create_mode_controls()
            main_layout.addWidget(controls_frame)
        
        # Info display
        self.info_label = self.create_info_label()
        main_layout.addWidget(self.info_label)
        
        self.setLayout(main_layout)
        self.update_info_display()
    
    def create_title_label(self) -> QLabel:
        """Create title label using dialog patterns."""
        if THEMING_AVAILABLE:
            return create_themed_label(self.title, 'header_label')
        else:
            label = QLabel(self.title)
            label.setAlignment(Qt.AlignmentFlag.AlignCenter)
            label.setStyleSheet("font-weight: bold; font-size: 14px; color: #2d3748;")
            return label
    
    def create_board_frame(self) -> QFrame:
        """Create the board frame using dialog theming patterns."""
        if THEMING_AVAILABLE:
            frame = create_themed_group_box("Chess Board")
            layout = QVBoxLayout()
            layout.addWidget(self.chess_board_core)
            frame.setLayout(layout)
            return frame
        else:
            frame = QFrame()
            frame.setStyleSheet("""
                QFrame {
                    background: #2d1810;
                    border: 2px solid #8b4513;
                    border-radius: 8px;
                    padding: 8px;
                }
            """)
            layout = QVBoxLayout()
            layout.addWidget(self.chess_board_core)
            frame.setLayout(layout)
            return frame
    
    def create_preset_controls(self) -> QFrame:
        """Create preset pattern control buttons."""
        if THEMING_AVAILABLE:
            frame = create_themed_group_box("Preset Patterns")
        else:
            frame = QGroupBox("Preset Patterns")
        
        layout = QVBoxLayout()
        
        # Create button group for exclusive selection
        self.preset_button_group = QButtonGroup()
        
        # Preset patterns
        presets = [
            ("Rook", "rook"),
            ("Bishop", "bishop"), 
            ("Queen", "queen"),
            ("Knight", "knight"),
            ("King", "king"),
            ("Global", "global")
        ]
        
        # Create buttons in rows
        button_layout = QHBoxLayout()
        for i, (name, pattern_id) in enumerate(presets):
            if THEMING_AVAILABLE:
                button = create_themed_button(name, 'secondary_button')
            else:
                button = QPushButton(name)
            
            button.setCheckable(True)
            button.clicked.connect(lambda checked, pid=pattern_id: self.apply_preset(pid))
            
            self.preset_buttons.append(button)
            self.preset_button_group.addButton(button)
            button_layout.addWidget(button)
            
            # Start new row after 3 buttons
            if i == 2:
                layout.addLayout(button_layout)
                button_layout = QHBoxLayout()
        
        # Add remaining buttons
        if button_layout.count() > 0:
            layout.addLayout(button_layout)
        
        frame.setLayout(layout)
        return frame
    
    def create_mode_controls(self) -> QFrame:
        """Create mode-specific control widgets."""
        if THEMING_AVAILABLE:
            frame = create_themed_group_box("Controls")
        else:
            frame = QGroupBox("Controls")
        
        layout = QVBoxLayout()
        
        if self.mode == BoardMode.PATTERN:
            # Paint mode controls for pattern editing
            paint_layout = QHBoxLayout()
            
            paint_modes = [
                ("Move", TileState.MOVE),
                ("Attack", TileState.ATTACK),
                ("Both", TileState.BOTH),
                ("Action", TileState.ACTION),
                ("Any", TileState.ANY)
            ]
            
            paint_group = QButtonGroup()
            for name, state in paint_modes:
                if THEMING_AVAILABLE:
                    button = create_themed_button(name, 'toggle_button')
                else:
                    button = QPushButton(name)
                
                button.setCheckable(True)
                button.clicked.connect(lambda checked, s=state: self.set_paint_mode(s))
                paint_group.addButton(button)
                paint_layout.addWidget(button)
                
                # Set default selection
                if state == TileState.MOVE:
                    button.setChecked(True)
            
            layout.addLayout(paint_layout)
        
        # Common controls
        clear_button = create_themed_button("Clear All", 'danger_button') if THEMING_AVAILABLE else QPushButton("Clear All")
        clear_button.clicked.connect(self.clear_pattern)
        layout.addWidget(clear_button)
        
        frame.setLayout(layout)
        return frame
    
    def create_info_label(self) -> QLabel:
        """Create info display label."""
        if THEMING_AVAILABLE:
            label = create_themed_label("", 'info_label')
        else:
            label = QLabel()
            label.setStyleSheet("color: #4a5568; font-size: 12px;")
        
        label.setAlignment(Qt.AlignmentFlag.AlignCenter)
        return label
    
    def apply_theming(self) -> None:
        """Apply consistent theming using dialog patterns."""
        if THEMING_AVAILABLE:
            apply_theme_to_widget(self, 'main_widget')
    
    def apply_preset(self, preset_name: str) -> None:
        """Apply a preset pattern."""
        if self.chess_board_core:
            self.chess_board_core.apply_preset_pattern(preset_name)
            self.preset_selected.emit(preset_name)
            self.update_info_display()
    
    def set_paint_mode(self, mode: int) -> None:
        """Set the paint mode for pattern editing."""
        self.current_paint_mode = mode
        if self.chess_board_core:
            self.chess_board_core.set_paint_mode(mode)
    
    def clear_pattern(self) -> None:
        """Clear all pattern data."""
        if self.chess_board_core:
            self.chess_board_core.clear_pattern()
            self.update_info_display()
    
    def update_info_display(self, *args) -> None:
        """Update the info display with current state."""
        if not self.info_label or not self.chess_board_core:
            return
        
        pattern = self.chess_board_core.get_pattern()
        piece_pos = self.chess_board_core.get_piece_position()
        
        # Count active tiles
        active_count = sum(1 for row in pattern for cell in row if cell > 0)
        
        info_text = f"Mode: {self.mode.title()} | Active Tiles: {active_count} | Piece: ({piece_pos[0]}, {piece_pos[1]})"
        self.info_label.setText(info_text)
    
    # Forward methods to core board
    
    def get_pattern(self) -> List[List[int]]:
        """Get the current pattern."""
        return self.chess_board_core.get_pattern() if self.chess_board_core else []
    
    def set_pattern(self, pattern: List[List[int]]) -> None:
        """Set the pattern."""
        if self.chess_board_core:
            self.chess_board_core.set_pattern(pattern)
    
    def get_piece_position(self) -> List[int]:
        """Get the piece position."""
        return self.chess_board_core.get_piece_position() if self.chess_board_core else [3, 3]
    
    def set_piece_position(self, position: List[int]) -> None:
        """Set the piece position."""
        if self.chess_board_core:
            self.chess_board_core.set_piece_position(position)
    
    def set_mode(self, mode: str) -> None:
        """Set the board mode."""
        self.mode = mode
        if self.chess_board_core:
            self.chess_board_core.set_mode(mode)
