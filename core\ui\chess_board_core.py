#!/usr/bin/env python3
"""
Core Chess Board Logic for Adventure Chess Creator

This module contains the essential chess board functionality separated from UI framework concerns.
The core logic can be embedded in dialogs or standalone widgets without duplication.

Extracted from centralized_board.py to eliminate redundancy with dialog base classes.
"""

from PyQt6.QtWidgets import QWidget, QGridLayout, QPushButton
from PyQt6.QtCore import Qt, pyqtSignal
from typing import List, Dict, Optional
import logging

logger = logging.getLogger(__name__)


class BoardMode:
    """Enumeration of supported board modes"""
    PATTERN = "pattern"          # Movement/attack pattern editing
    RANGE = "range"             # Target range selection
    AREA = "area"               # Area effect configuration
    ADJACENCY = "adjacency"     # Adjacency pattern editing
    PREVIEW = "preview"         # Read-only preview mode
    SELECTION = "selection"     # Multi-select mode


class TileState:
    """Enumeration of tile states for different modes"""
    EMPTY = 0
    MOVE = 1
    ATTACK = 2
    BOTH = 3
    ACTION = 4
    ANY = 5
    SELECTED = 1  # For boolean modes
    BLOCKED = -1


class ChessBoardCore(QWidget):
    """
    Core chess board widget containing only essential board logic.
    
    This class provides the fundamental chess board functionality without
    UI framework concerns, making it suitable for embedding in dialogs
    or other containers that provide their own styling and layout.
    """
    
    # Signals for component communication
    pattern_changed = pyqtSignal(list)  # Emitted when pattern changes (8x8 array)
    piece_position_changed = pyqtSignal(list)  # Emitted when piece position changes
    tile_clicked = pyqtSignal(int, int, int)  # Emitted on tile click (row, col, state)
    
    def __init__(self, parent: Optional[QWidget] = None, mode: str = BoardMode.PATTERN,
                 tile_size: int = 50):
        """
        Initialize the core chess board.
        
        Args:
            parent: Parent widget
            mode: Board mode (see BoardMode class for options)
            tile_size: Size of each tile in pixels
        """
        super().__init__(parent)
        
        # Configuration
        self.mode = mode
        self.tile_size = tile_size
        
        # Data storage - standardized 8x8 array format
        self.pattern: List[List[int]] = [[0 for _ in range(8)] for _ in range(8)]
        self.piece_position: List[int] = [3, 3]  # Default center position
        
        # UI components
        self.grid_buttons: List[List[QPushButton]] = []
        
        # Mode-specific settings
        self.paint_mode = TileState.MOVE  # Current paint mode for pattern editing
        self.continue_off_board = False   # Whether patterns continue off board
        
        self.setup_board()
    
    def setup_board(self) -> None:
        """Setup the core 8x8 chess board grid."""
        layout = QGridLayout()
        layout.setSpacing(0)  # No spacing between tiles for seamless chess board
        layout.setContentsMargins(2, 2, 2, 2)  # Minimal border around the board
        
        # Initialize grid buttons
        self.grid_buttons = []
        for row in range(8):
            button_row = []
            for col in range(8):
                button = QPushButton()
                button.setFixedSize(self.tile_size, self.tile_size)
                button.clicked.connect(lambda checked, r=row, c=col: self.handle_tile_click(r, c))
                
                # Set base chess board styling
                self.apply_base_tile_style(button, row, col)
                
                button_row.append(button)
                layout.addWidget(button, row, col)
            
            self.grid_buttons.append(button_row)
        
        self.setLayout(layout)
        self.update_display()
    
    def apply_base_tile_style(self, button: QPushButton, row: int, col: int) -> None:
        """Apply base chess board styling to a tile."""
        # Determine if tile is light or dark
        is_light = (row + col) % 2 == 0
        
        base_color = "#f7fafc" if is_light else "#4a5568"  # Light gray / Dark gray
        hover_color = "#e2e8f0" if is_light else "#2d3748"  # Slightly darker on hover
        
        button.setStyleSheet(f"""
            QPushButton {{
                background-color: {base_color};
                border: 1px solid #718096;
                font-weight: bold;
                font-size: 12px;
            }}
            QPushButton:hover {{
                background-color: {hover_color};
                border: 2px solid #4299e1;
            }}
            QPushButton:pressed {{
                background-color: #cbd5e0;
            }}
        """)
    
    def handle_tile_click(self, row: int, col: int) -> None:
        """Handle tile click events."""
        if self.mode == BoardMode.PREVIEW:
            return  # No interaction in preview mode
        
        current_state = self.pattern[row][col]
        
        if self.mode == BoardMode.PATTERN:
            # Cycle through pattern states or use paint mode
            new_state = self.paint_mode
        elif self.mode in [BoardMode.RANGE, BoardMode.AREA, BoardMode.ADJACENCY, BoardMode.SELECTION]:
            # Toggle boolean state
            new_state = TileState.SELECTED if current_state == TileState.EMPTY else TileState.EMPTY
        else:
            new_state = current_state
        
        self.pattern[row][col] = new_state
        self.update_tile_display(row, col)
        
        # Emit signals
        self.tile_clicked.emit(row, col, new_state)
        self.pattern_changed.emit(self.pattern)
    
    def update_tile_display(self, row: int, col: int) -> None:
        """Update the display for a specific tile."""
        if not self.grid_buttons or row < 0 or row >= 8 or col < 0 or col >= 8:
            return
        
        button = self.grid_buttons[row][col]
        state = self.pattern[row][col]
        
        # Get colors based on mode and state
        colors = self.get_state_colors(state)
        
        # Apply styling
        is_light = (row + col) % 2 == 0
        base_color = "#f7fafc" if is_light else "#4a5568"
        
        if colors['background']:
            bg_color = colors['background']
        else:
            bg_color = base_color
        
        text_color = colors.get('text', '#2d3748')
        
        # Show piece position indicator
        piece_indicator = ""
        if [row, col] == self.piece_position:
            piece_indicator = "♔"  # King symbol for piece position
        
        # Show state indicator
        state_text = self.get_state_text(state)
        display_text = f"{piece_indicator}{state_text}".strip()
        
        button.setText(display_text)
        button.setStyleSheet(f"""
            QPushButton {{
                background-color: {bg_color};
                color: {text_color};
                border: 1px solid #718096;
                font-weight: bold;
                font-size: 12px;
            }}
            QPushButton:hover {{
                border: 2px solid #4299e1;
            }}
        """)
    
    def get_state_colors(self, state: int) -> Dict[str, str]:
        """Get colors for a tile state based on current mode."""
        if self.mode == BoardMode.PATTERN:
            return self.get_pattern_state_colors(state)
        elif self.mode in [BoardMode.RANGE, BoardMode.AREA, BoardMode.ADJACENCY, BoardMode.SELECTION]:
            return self.get_boolean_state_colors(state)
        else:
            return {'background': None, 'text': '#2d3748'}
    
    def get_pattern_state_colors(self, state: int) -> Dict[str, str]:
        """Get colors for pattern mode states (movement/attack patterns)."""
        color_map = {
            TileState.EMPTY: {'background': None, 'text': '#2d3748'},
            TileState.MOVE: {'background': '#48bb78', 'text': 'white'},      # Green for movement
            TileState.ATTACK: {'background': '#f56565', 'text': 'white'},    # Red for attack
            TileState.BOTH: {'background': '#ed8936', 'text': 'white'},      # Orange for both
            TileState.ACTION: {'background': '#9f7aea', 'text': 'white'},    # Purple for action
            TileState.ANY: {'background': '#4299e1', 'text': 'white'},       # Blue for any
        }
        return color_map.get(state, {'background': None, 'text': '#2d3748'})
    
    def get_boolean_state_colors(self, state: int) -> Dict[str, str]:
        """Get colors for boolean mode states (range/area/adjacency)."""
        if state > 0:
            return {'background': '#4299e1', 'text': 'white'}  # Blue for selected
        else:
            return {'background': None, 'text': '#2d3748'}
    
    def get_state_text(self, state: int) -> str:
        """Get display text for a tile state."""
        if self.mode == BoardMode.PATTERN:
            state_map = {
                TileState.EMPTY: "",
                TileState.MOVE: "M",
                TileState.ATTACK: "A", 
                TileState.BOTH: "B",
                TileState.ACTION: "X",
                TileState.ANY: "*"
            }
            return state_map.get(state, "")
        elif self.mode in [BoardMode.RANGE, BoardMode.AREA, BoardMode.ADJACENCY, BoardMode.SELECTION]:
            return "●" if state > 0 else ""
        else:
            return ""
    
    def update_display(self) -> None:
        """Update the display of all tiles."""
        for row in range(8):
            for col in range(8):
                self.update_tile_display(row, col)
    
    # Data Management Methods
    
    def set_pattern(self, pattern_array: List[List[int]]) -> None:
        """Set the pattern from an 8x8 array."""
        if not pattern_array or len(pattern_array) != 8:
            logger.warning("Invalid pattern array provided")
            return
        
        for row in pattern_array:
            if len(row) != 8:
                logger.warning("Invalid pattern array row length")
                return
        
        self.pattern = [row[:] for row in pattern_array]  # Deep copy
        self.update_display()
        self.pattern_changed.emit(self.pattern)
    
    def get_pattern(self) -> List[List[int]]:
        """Get the current pattern as an 8x8 array."""
        return [row[:] for row in self.pattern]  # Deep copy
    
    def set_piece_position(self, position: List[int]) -> None:
        """Set the piece position."""
        if len(position) == 2 and 0 <= position[0] < 8 and 0 <= position[1] < 8:
            old_pos = self.piece_position[:]
            self.piece_position = position[:]
            
            # Update display for old and new positions
            self.update_tile_display(old_pos[0], old_pos[1])
            self.update_tile_display(position[0], position[1])
            
            self.piece_position_changed.emit(self.piece_position)
    
    def get_piece_position(self) -> List[int]:
        """Get the current piece position."""
        return self.piece_position[:]
    
    def clear_pattern(self) -> None:
        """Clear all pattern data."""
        self.pattern = [[0 for _ in range(8)] for _ in range(8)]
        self.update_display()
        self.pattern_changed.emit(self.pattern)
    
    def set_paint_mode(self, mode: int) -> None:
        """Set the paint mode for pattern editing."""
        self.paint_mode = mode
    
    def set_mode(self, mode: str) -> None:
        """Change the board mode."""
        self.mode = mode
        self.update_display()

    # Preset Pattern Methods

    def apply_preset_pattern(self, preset_name: str) -> None:
        """Apply a preset pattern to the board."""
        patterns = {
            'rook': self._create_rook_pattern(),
            'bishop': self._create_bishop_pattern(),
            'queen': self._create_queen_pattern(),
            'knight': self._create_knight_pattern(),
            'king': self._create_king_pattern(),
            'global': self._create_global_pattern()
        }

        if preset_name in patterns:
            self.set_pattern(patterns[preset_name])

    def _create_rook_pattern(self) -> List[List[int]]:
        """Create rook movement pattern (orthogonal)."""
        pattern = [[0 for _ in range(8)] for _ in range(8)]
        row, col = self.piece_position

        # Horizontal and vertical lines
        for i in range(8):
            if i != col:
                pattern[row][i] = TileState.MOVE
            if i != row:
                pattern[i][col] = TileState.MOVE

        return pattern

    def _create_bishop_pattern(self) -> List[List[int]]:
        """Create bishop movement pattern (diagonal)."""
        pattern = [[0 for _ in range(8)] for _ in range(8)]
        row, col = self.piece_position

        # Diagonal lines
        for i in range(8):
            for j in range(8):
                if i != row and j != col and abs(i - row) == abs(j - col):
                    pattern[i][j] = TileState.MOVE

        return pattern

    def _create_queen_pattern(self) -> List[List[int]]:
        """Create queen movement pattern (orthogonal + diagonal)."""
        pattern = [[0 for _ in range(8)] for _ in range(8)]
        row, col = self.piece_position

        # Combine rook and bishop patterns
        for i in range(8):
            for j in range(8):
                if i == row or j == col or abs(i - row) == abs(j - col):
                    if i != row or j != col:  # Don't mark piece position
                        pattern[i][j] = TileState.MOVE

        return pattern

    def _create_knight_pattern(self) -> List[List[int]]:
        """Create knight movement pattern (L-shaped)."""
        pattern = [[0 for _ in range(8)] for _ in range(8)]
        row, col = self.piece_position

        # Knight moves: 2+1 in L-shape
        knight_moves = [(-2, -1), (-2, 1), (-1, -2), (-1, 2),
                       (1, -2), (1, 2), (2, -1), (2, 1)]

        for dr, dc in knight_moves:
            new_row, new_col = row + dr, col + dc
            if 0 <= new_row < 8 and 0 <= new_col < 8:
                pattern[new_row][new_col] = TileState.MOVE

        return pattern

    def _create_king_pattern(self) -> List[List[int]]:
        """Create king movement pattern (adjacent squares)."""
        pattern = [[0 for _ in range(8)] for _ in range(8)]
        row, col = self.piece_position

        # King moves: one square in any direction
        for dr in [-1, 0, 1]:
            for dc in [-1, 0, 1]:
                if dr == 0 and dc == 0:
                    continue  # Skip piece position
                new_row, new_col = row + dr, col + dc
                if 0 <= new_row < 8 and 0 <= new_col < 8:
                    pattern[new_row][new_col] = TileState.MOVE

        return pattern

    def _create_global_pattern(self) -> List[List[int]]:
        """Create global pattern (all squares)."""
        pattern = [[TileState.MOVE for _ in range(8)] for _ in range(8)]
        # Don't mark piece position
        row, col = self.piece_position
        pattern[row][col] = TileState.EMPTY
        return pattern
