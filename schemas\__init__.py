"""
Adventure Chess Pydantic Schemas Package

Location: schemas/

Provides strongly-typed data models for pieces, abilities, and game components.
This package contains:
- base.py: Core data types and enums
- piece_schema.py: Piece and movement models
- ability_schema.py: Ability data models
- ability_tags.py: Tag-specific validation models
- data_manager.py: Pydantic-based data management
- (migration.py removed - was outdated development tooling)

Used by: All editors and data handlers for validation and type safety
"""

from .base import (
    Coordinate, 
    MovementType, 
    RechargeType, 
    ActivationMode,
    PieceRole,
    Pattern8x8,
    RangeMask8x8
)

from .piece_schema import Piece, Movement
from .ability_schema import Ability
from .ability_tags import *
# Note: Data manager imports moved to avoid circular imports
# Import data managers directly when needed to prevent circular dependencies
pydantic_data_manager = None  # Will be set by data manager module
PydanticDataManager = None    # Will be set by data manager module
# Migration module removed - was outdated development tooling

__version__ = "1.0.0"

__all__ = [
    # Base types
    "Coordinate",
    "MovementType",
    "RechargeType",
    "ActivationMode",
    "PieceRole",
    "Pattern8x8",
    "RangeMask8x8",

    # Main models
    "Piece",
    "Movement",
    "Ability",

    # Data management
    "PydanticDataManager",
    "pydantic_data_manager",

    # Migration utilities removed - were outdated development tools

    # Ability tag models (imported from ability_tags)
    "TAG_MODEL_REGISTRY",
    "get_tag_model",
    "validate_tag_data",
]
