#!/usr/bin/env python3
"""
Reusable Pattern Editor Dialog for Adventure Chess
Used by both piece editor and ability editor for movement/attack patterns
"""

from PyQt6.QtWidgets import (QVBoxLayout, QPushButton, QCheckBox)
from PyQt6.QtCore import Qt
from PyQt6.QtWidgets import <PERSON><PERSON><PERSON><PERSON>
from typing import Dict, Any

from .base_dialog import BaseChessBoardDialog

class PatternEditorDialog(BaseChessBoardDialog):
    """
    Reusable pattern editor dialog that supports both single pattern and dual pattern modes
    """

    # Class-level state storage for checkbox persistence
    _last_checkbox_state = {
        'starting_square_checked': False,
        'continue_off_board_checked': False
    }

    def __init__(self, movement_pattern=None, attack_pattern=None, title="Pattern Editor", parent=None, checkbox_states=None, highlighted_preset=None):
        # Initialize data before calling parent constructor
        self.highlighted_preset = highlighted_preset
        self._initialize_data(movement_pattern, attack_pattern, checkbox_states)

        # Call parent constructor with standardized parameters
        super().__init__(parent, title, (800, 600), "pattern")

    def _initialize_data(self, movement_pattern, attack_pattern, checkbox_states):
        """Initialize dialog data before UI setup."""
        # Determine if we're in dual pattern mode (buff/debuff) or single pattern mode (piece)
        self.dual_mode = attack_pattern is not None

        # For dual mode, we use a single pattern with 3-color system like piece creator
        # 0 = empty, 1 = move only (blue), 2 = capture only (red), 3 = move and capture (purple)
        if self.dual_mode:
            # Convert dual patterns to single 3-color pattern if provided
            if movement_pattern is not None and attack_pattern is not None:
                self.pattern = self.convert_dual_to_single(movement_pattern, attack_pattern)
            else:
                self.pattern = [[0 for _ in range(8)] for _ in range(8)]
        else:
            # Single mode uses the pattern directly
            if movement_pattern is None:
                self.pattern = [[0 for _ in range(8)] for _ in range(8)]
            else:
                self.pattern = [row[:] for row in movement_pattern]  # Deep copy

        # Piece position
        self.piece_pos = [3, 3]  # Start in center

        # Initialize checkbox states from parameter or defaults
        if checkbox_states is not None:
            self.include_starting_square = checkbox_states.get('starting_square_checked', False)
            self.continue_off_board = checkbox_states.get('continue_off_board_checked', False)
        else:
            self.include_starting_square = False
            self.continue_off_board = False
    
    def convert_dual_to_single(self, movement_pattern, attack_pattern):
        """Convert separate movement and attack patterns to single 3-color pattern"""
        pattern = [[0 for _ in range(8)] for _ in range(8)]
        
        for r in range(8):
            for c in range(8):
                move_enabled = movement_pattern[r][c] > 0 if isinstance(movement_pattern[r][c], int) else bool(movement_pattern[r][c])
                attack_enabled = attack_pattern[r][c] > 0 if isinstance(attack_pattern[r][c], int) else bool(attack_pattern[r][c])
                
                if move_enabled and attack_enabled:
                    pattern[r][c] = 3  # Both
                elif move_enabled:
                    pattern[r][c] = 1  # Move only
                elif attack_enabled:
                    pattern[r][c] = 2  # Attack only
                else:
                    pattern[r][c] = 0  # Empty
        
        return pattern
    
    def convert_single_to_dual(self):
        """Convert single 3-color pattern to separate movement and attack patterns"""
        movement_pattern = [[0 for _ in range(8)] for _ in range(8)]
        attack_pattern = [[0 for _ in range(8)] for _ in range(8)]
        
        for r in range(8):
            for c in range(8):
                state = self.pattern[r][c]
                if state == 1 or state == 3:  # Move only or both
                    movement_pattern[r][c] = 1
                if state == 2 or state == 3:  # Attack only or both
                    attack_pattern[r][c] = 1
        
        return movement_pattern, attack_pattern
    
    def setup_controls(self):
        """Setup the control widgets on the left side."""
        # Instructions
        instructions = self.create_description_label(
            "Click tiles to cycle: Empty → Move → Attack → Both → Action → Any"
        )
        self.left_layout.addWidget(instructions)

        # Options section
        options_group = self.create_section_group("Options")
        options_layout = QVBoxLayout()

        self.starting_square_check = QCheckBox("Include Starting Square")
        self.starting_square_check.setToolTip("Allow targeting the piece's starting position")
        self.starting_square_check.stateChanged.connect(self.on_starting_square_changed)
        self.starting_square_check.stateChanged.connect(self.mark_data_changed)
        options_layout.addWidget(self.starting_square_check)

        self.continue_off_board_check = QCheckBox("Continue Off Board")
        self.continue_off_board_check.setToolTip("continues board pattern off edges of map")
        self.continue_off_board_check.stateChanged.connect(self.on_continue_off_board_changed)
        self.continue_off_board_check.stateChanged.connect(self.mark_data_changed)
        options_layout.addWidget(self.continue_off_board_check)

        # Set checkbox states from initialization
        self.starting_square_check.setChecked(self.include_starting_square)
        self.continue_off_board_check.setChecked(self.continue_off_board)

        options_group.setLayout(options_layout)
        self.left_layout.addWidget(options_group)

        # Quick Patterns section
        patterns_group = self.create_section_group("Quick Patterns")
        patterns_layout = QVBoxLayout()

        # Chess piece pattern buttons in vertical layout
        pattern_buttons = [
            ("♜", "rook", "Orthogonal lines (like Rook)"),
            ("♝", "bishop", "Diagonal lines (like Bishop)"),
            ("♛", "queen", "All directions (like Queen)"),
            ("♞", "knight", "L-shaped moves (like Knight)"),
            ("♚", "king", "Adjacent squares (like King)"),
            ("🌐", "global", "Entire board range")
        ]

        # Track pattern buttons for highlighting
        self.pattern_preset_buttons = []
        self.current_preset_type = self.highlighted_preset  # Initialize with highlighted preset

        for symbol, preset_type, tooltip in pattern_buttons:
            btn = QPushButton(f"{symbol} {tooltip.split('(')[0].strip()}")
            btn.setFixedHeight(30)
            btn.setToolTip(f"{tooltip}")

            # Store button reference with preset type
            self.pattern_preset_buttons.append((btn, preset_type))

            # Set initial styling based on highlighted preset
            self.update_preset_button_style(btn, preset_type)

            btn.clicked.connect(lambda _, pt=preset_type: self.select_pattern_preset(pt))
            btn.clicked.connect(self.mark_data_changed)
            patterns_layout.addWidget(btn)

        # Clear button
        clear_preset_btn = QPushButton("Clear All")
        clear_preset_btn.clicked.connect(self.clear_pattern)
        clear_preset_btn.clicked.connect(self.mark_data_changed)
        clear_preset_btn.setToolTip("Clear all tiles on the board")
        patterns_layout.addWidget(clear_preset_btn)

        patterns_group.setLayout(patterns_layout)
        self.left_layout.addWidget(patterns_group)

    def setup_chess_board(self):
        """Setup the chess board on the right side."""
        super().setup_chess_board()

        # Set initial pattern
        if hasattr(self, 'pattern') and self.pattern:
            self.chess_board.set_pattern(self.pattern)
        if hasattr(self, 'piece_pos'):
            self.chess_board.set_piece_position(self.piece_pos)

        # Connect additional signals
        if hasattr(self.chess_board, 'pattern_changed'):
            self.chess_board.pattern_changed.connect(self.on_pattern_changed)
        if hasattr(self.chess_board, 'piece_position_changed'):
            self.chess_board.piece_position_changed.connect(self.on_piece_position_changed)

    def validate_data(self) -> bool:
        """Validate the current dialog data."""
        return True  # Pattern editor always has valid data

    def collect_data(self) -> Dict[str, Any]:
        """Collect data from the dialog."""
        return {
            'pattern': self.pattern,
            'piece_position': self.piece_pos,
            'checkbox_states': {
                'starting_square_checked': self.starting_square_check.isChecked(),
                'continue_off_board_checked': self.continue_off_board_check.isChecked()
            },
            'dual_mode': self.dual_mode
        }

    def on_pattern_changed(self, pattern):
        """Handle pattern changes from the enhanced board widget"""
        self.pattern = pattern

    def on_piece_position_changed(self, position):
        """Handle piece position changes from the enhanced board widget"""
        self.piece_pos = position

    def move_piece(self, row, col):
        """Move the piece to a new position"""
        self.piece_pos = [row, col]
        if hasattr(self, 'chess_board'):
            self.chess_board.set_piece_position(self.piece_pos[0], self.piece_pos[1])

    def on_starting_square_changed(self, state):
        """Handle starting square checkbox change"""
        self.include_starting_square = state == Qt.CheckState.Checked.value

    def on_continue_off_board_changed(self, state):
        """Handle continue off board checkbox change"""
        self.continue_off_board = state == Qt.CheckState.Checked.value

    def save_checkbox_states(self):
        """Save current checkbox states for persistence (both class-level and JSON)"""
        # Update class-level state for immediate use
        PatternEditorDialog._last_checkbox_state = {
            'starting_square_checked': self.starting_square_check.isChecked(),
            'continue_off_board_checked': self.continue_off_board_check.isChecked()
        }

    def restore_checkbox_states(self):
        """Restore checkbox states from saved state"""
        state = PatternEditorDialog._last_checkbox_state
        self.starting_square_check.setChecked(state.get('starting_square_checked', False))
        self.continue_off_board_check.setChecked(state.get('continue_off_board_checked', False))

    def reset_to_defaults(self):
        """Reset checkbox states to default values"""
        self.starting_square_check.setChecked(False)
        self.continue_off_board_check.setChecked(False)
        # Update class-level state to reflect defaults
        PatternEditorDialog._last_checkbox_state = {
            'starting_square_checked': False,
            'continue_off_board_checked': False
        }

    def accept(self):
        """Override accept to save checkbox states before closing"""
        self.save_checkbox_states()
        super().accept()
    

    
    def update_visual(self):
        """Update the visual representation using the enhanced board widget"""
        if hasattr(self, 'chess_board'):
            self.chess_board.set_pattern_from_array(self.pattern)
            self.chess_board.set_piece_position(self.piece_pos[0], self.piece_pos[1])
    
    def clear_pattern(self):
        """Clear the current pattern"""
        self.pattern = [[0 for _ in range(8)] for _ in range(8)]
        # Clear preset selection when manually clearing
        self.current_preset_type = None
        self.update_all_preset_button_styles()
        if hasattr(self, 'chess_board'):
            self.chess_board.set_pattern_from_array(self.pattern)

    def update_preset_button_style(self, btn, preset_type):
        """Update the style of a single preset button based on selection state"""
        if self.current_preset_type == preset_type:
            # Highlighted style for the current preset
            btn.setStyleSheet("""
                QPushButton {
                    font-size: 16px;
                    font-weight: bold;
                    border: 3px solid #66aaff;
                    border-radius: 6px;
                    background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                                              stop:0 #4488cc, stop:1 #3366aa);
                    color: white;
                    padding: 2px;
                }
                QPushButton:hover {
                    background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                                              stop:0 #5599dd, stop:1 #4477bb);
                    border-color: #77bbff;
                }
                QPushButton:pressed {
                    background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                                              stop:0 #2255aa, stop:1 #1144aa);
                    border-color: #4488cc;
                }
            """)
        else:
            # Normal style
            btn.setStyleSheet("""
                QPushButton {
                    font-size: 16px;
                    font-weight: bold;
                    border: 2px solid #555;
                    border-radius: 6px;
                    background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                                              stop:0 #3a3a3a, stop:1 #2a2a2a);
                    color: white;
                    padding: 2px;
                }
                QPushButton:hover {
                    background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                                              stop:0 #4a4a4a, stop:1 #3a3a3a);
                    border-color: #66aaff;
                }
                QPushButton:pressed {
                    background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                                              stop:0 #1a1a1a, stop:1 #2a2a2a);
                    border-color: #4488cc;
                }
            """)

    def update_all_preset_button_styles(self):
        """Update all preset button styles based on current selection"""
        for btn, preset_type in self.pattern_preset_buttons:
            self.update_preset_button_style(btn, preset_type)

    def select_pattern_preset(self, preset_type):
        """Select a pattern preset and update button highlighting"""
        # Update current selection
        self.current_preset_type = preset_type

        # Update all button styles
        self.update_all_preset_button_styles()

        # Apply the preset pattern
        self.apply_pattern_preset(preset_type)
    
    def apply_pattern_preset(self, preset_type):
        """Apply a preset pattern based on chess piece movement with auto continue off board"""
        # Clear current pattern
        self.pattern = [[0 for _ in range(8)] for _ in range(8)]

        piece_r, piece_c = self.piece_pos

        # Auto-set continue off board based on preset type
        # King and Knight should NOT continue off board, all others should
        should_continue_off_board = preset_type not in ['king', 'knight']
        self.continue_off_board_check.setChecked(should_continue_off_board)
        self.continue_off_board = should_continue_off_board
        
        if preset_type == "rook":
            # Rook: All squares in orthogonal lines
            # Horizontal line
            for c in range(8):
                if c != piece_c:
                    self.pattern[piece_r][c] = 3  # Move and attack
            # Vertical line
            for r in range(8):
                if r != piece_r:
                    self.pattern[r][piece_c] = 3  # Move and attack
                    
        elif preset_type == "bishop":
            # Bishop: All squares in diagonal lines
            for r in range(8):
                for c in range(8):
                    if r != piece_r and c != piece_c:
                        if abs(r - piece_r) == abs(c - piece_c):
                            self.pattern[r][c] = 3  # Move and attack
                            
        elif preset_type == "queen":
            # Queen: Combination of rook and bishop
            # Orthogonal lines
            for c in range(8):
                if c != piece_c:
                    self.pattern[piece_r][c] = 3
            for r in range(8):
                if r != piece_r:
                    self.pattern[r][piece_c] = 3
            # Diagonal lines
            for r in range(8):
                for c in range(8):
                    if r != piece_r and c != piece_c:
                        if abs(r - piece_r) == abs(c - piece_c):
                            self.pattern[r][c] = 3
                            
        elif preset_type == "knight":
            # Knight: L-shaped moves
            knight_moves = [(-2, -1), (-2, 1), (-1, -2), (-1, 2), 
                           (1, -2), (1, 2), (2, -1), (2, 1)]
            for dr, dc in knight_moves:
                r, c = piece_r + dr, piece_c + dc
                if 0 <= r < 8 and 0 <= c < 8:
                    self.pattern[r][c] = 3  # Move and attack
                    
        elif preset_type == "king":
            # King: Adjacent squares (8 directions)
            for dr in [-1, 0, 1]:
                for dc in [-1, 0, 1]:
                    if dr == 0 and dc == 0:
                        continue  # Skip piece position
                    r, c = piece_r + dr, piece_c + dc
                    if 0 <= r < 8 and 0 <= c < 8:
                        self.pattern[r][c] = 3  # Move and attack
                        
        elif preset_type == "global":
            # Global: Entire board except piece position
            for r in range(8):
                for c in range(8):
                    if [r, c] != self.piece_pos:
                        self.pattern[r][c] = 3  # Move and attack
        
        if hasattr(self, 'chess_board'):
            self.chess_board.set_pattern_from_array(self.pattern)
    
    def get_movement_pattern(self):
        """Get the movement pattern"""
        if self.dual_mode:
            movement_pattern, _ = self.convert_single_to_dual()
            return movement_pattern
        else:
            return [row[:] for row in self.pattern]  # Return deep copy
    
    def get_attack_pattern(self):
        """Get the attack pattern (only valid in dual mode)"""
        if self.dual_mode:
            _, attack_pattern = self.convert_single_to_dual()
            return attack_pattern
        return None
    
    def get_pattern(self):
        """Get the single pattern (for backward compatibility)"""
        return [row[:] for row in self.pattern]  # Return deep copy
    
    def set_movement_pattern(self, pattern):
        """Set the movement pattern"""
        if pattern and len(pattern) == 8 and len(pattern[0]) == 8:
            if self.dual_mode:
                # For dual mode, we need to merge with existing attack pattern
                _, attack_pattern = self.convert_single_to_dual()
                self.pattern = self.convert_dual_to_single(pattern, attack_pattern)
            else:
                self.pattern = [row[:] for row in pattern]  # Deep copy
            if hasattr(self, 'chess_board'):
                self.chess_board.set_pattern_from_array(self.pattern)

    def set_attack_pattern(self, pattern):
        """Set the attack pattern (only valid in dual mode)"""
        if self.dual_mode and pattern and len(pattern) == 8 and len(pattern[0]) == 8:
            # For dual mode, we need to merge with existing movement pattern
            movement_pattern, _ = self.convert_single_to_dual()
            self.pattern = self.convert_dual_to_single(movement_pattern, pattern)
            if hasattr(self, 'chess_board'):
                self.chess_board.set_pattern_from_array(self.pattern)

    def get_checkbox_states(self):
        """Get the current checkbox states"""
        return {
            'starting_square_checked': self.starting_square_check.isChecked(),
            'continue_off_board_checked': self.continue_off_board_check.isChecked()
        }


# Convenience functions for different use cases

def edit_single_pattern(initial_pattern=None, title="Edit Pattern", parent=None, checkbox_states=None):
    """
    Edit a single pattern (for piece editor compatibility)
    Returns: (pattern, checkbox_states) or (None, None) if cancelled
    """
    dialog = PatternEditorDialog(initial_pattern, None, title, parent, checkbox_states)
    if dialog.exec() == QDialog.DialogCode.Accepted:
        return dialog.get_pattern(), dialog.get_checkbox_states()
    return None, None

def edit_dual_patterns(movement_pattern=None, attack_pattern=None, title="Edit Movement & Attack Patterns", parent=None, checkbox_states=None):
    """
    Edit both movement and attack patterns (for ability editor)
    Returns: (movement_pattern, attack_pattern, checkbox_states) or (None, None, None) if cancelled
    """
    dialog = PatternEditorDialog(movement_pattern, attack_pattern, title, parent, checkbox_states)
    if dialog.exec() == QDialog.DialogCode.Accepted:
        return dialog.get_movement_pattern(), dialog.get_attack_pattern(), dialog.get_checkbox_states()
    return None, None, None