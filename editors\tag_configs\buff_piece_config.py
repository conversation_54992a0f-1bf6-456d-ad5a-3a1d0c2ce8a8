"""
Buff Piece tag configuration for the Adventure Chess Creator ability editor.
Handles temporary enhancement of target pieces with stat boosts or abilities.
"""

from typing import Dict, Any
from PyQt6.QtWidgets import (
    QVBoxLayout, QFormLayout, QGroupBox, QLabel, QSpinBox, QCheckBox, QPushButton
)
from PyQt6.QtCore import Qt

from .base_tag_config import BaseTagConfig
from core.ui import InlinePieceSelector, InlineAbilitySelector


class BuffPieceConfig(BaseTagConfig):
    """Configuration for buffPiece tag - temporary piece enhancement."""
    
    def __init__(self, editor_instance):
        super().__init__(editor_instance, "buffPiece")
    
    def get_title(self) -> str:
        return "⬆️ Buff Piece Configuration"
    
    def create_ui(self, parent_layout) -> None:
        """Create the buff piece configuration UI."""
        try:
            # Main group box
            group = QGroupBox(self.get_title())
            layout = QVBoxLayout()
            
            # Description
            description = QLabel("Enhance target pieces with temporary abilities or stat boosts.")
            description.setWordWrap(True)
            layout.addWidget(description)
            
            # Target piece selector
            target_selector = InlinePieceSelector(self.editor, "Target Pieces", allow_costs=True)
            self.store_widget("buff_target_selector", target_selector)
            layout.addWidget(target_selector)
            
            # Buff options
            options_group = QGroupBox("Buff Options")
            options_layout = QVBoxLayout()

            # Duration checkbox
            duration_check = QCheckBox("Duration")
            duration_check.setToolTip("Enable to set specific duration in turns")
            duration_check.setChecked(False)  # Default to unchecked (indefinite)
            duration_check.toggled.connect(self.on_duration_toggled)
            self.store_widget("buff_duration_check", duration_check)
            options_layout.addWidget(duration_check)

            # Duration spinner (initially hidden)
            duration_spin = QSpinBox()
            duration_spin.setRange(1, 99)
            duration_spin.setValue(1)
            duration_spin.setToolTip("Duration in turns")
            duration_spin.setVisible(False)
            self.store_widget("buff_duration_spin", duration_spin)
            options_layout.addWidget(duration_spin)

            # Movement boost with range editor
            movement_check = QCheckBox("Increase Movement Range")
            movement_check.setToolTip("Temporarily increases piece movement range")
            movement_check.toggled.connect(self.on_movement_toggled)
            self.store_widget("buff_movement_check", movement_check)
            options_layout.addWidget(movement_check)

            # Movement range editor button (initially hidden)
            movement_range_btn = QPushButton("Edit Movement Range")
            movement_range_btn.setToolTip("Configure movement range pattern for buff")
            movement_range_btn.setVisible(False)
            movement_range_btn.clicked.connect(self.edit_movement_range)
            self.store_widget("buff_movement_range_btn", movement_range_btn)
            options_layout.addWidget(movement_range_btn)

            # Grant additional abilities with inline selector
            ability_check = QCheckBox("Grant Additional Abilities")
            ability_check.setToolTip("Temporarily grants new abilities to the piece")
            self.store_widget("buff_ability_check", ability_check)
            options_layout.addWidget(ability_check)

            # Ability selector (initially hidden)
            ability_selector = InlineAbilitySelector(self.editor, "Additional Abilities")
            ability_selector.setVisible(False)
            self.store_widget("buff_ability_selector", ability_selector)
            options_layout.addWidget(ability_selector)

            # Connect checkboxes to show/hide related controls
            ability_check.toggled.connect(lambda checked: ability_selector.setVisible(checked))

            options_group.setLayout(options_layout)
            layout.addWidget(options_group)

            group.setLayout(layout)
            parent_layout.addWidget(group)

            self.log_debug("Buff piece configuration UI created successfully")

        except Exception as e:
            self.log_error(f"Error creating buff piece UI: {e}")

    def on_duration_toggled(self, checked):
        """Handle duration checkbox toggle."""
        try:
            duration_spin = self.get_widget_by_name("buff_duration_spin")
            if duration_spin:
                duration_spin.setVisible(checked)
                if not checked:
                    duration_spin.setValue(1)
            self.log_debug(f"Duration toggled: {checked}")
        except Exception as e:
            self.log_error(f"Error handling duration toggle: {e}")

    def on_movement_toggled(self, checked):
        """Handle movement checkbox toggle."""
        try:
            movement_btn = self.get_widget_by_name("buff_movement_range_btn")
            if movement_btn:
                movement_btn.setVisible(checked)
            self.log_debug(f"Movement toggled: {checked}")
        except Exception as e:
            self.log_error(f"Error handling movement toggle: {e}")

    def edit_movement_range(self):
        """Open movement range editor dialog."""
        try:
            from dialogs.range_editor_dialog import edit_target_range

            # Get current pattern or create default
            current_pattern = getattr(self, 'movement_range_pattern', None)
            if current_pattern is None:
                current_pattern = [[False] * 8 for _ in range(8)]

            # Open range editor
            result = edit_target_range(self.editor, current_pattern, "Movement Range Pattern")
            if result is not None:
                self.movement_range_pattern = result
                self.log_debug("Movement range pattern updated")

                # Mark as changed
                if hasattr(self.editor, 'mark_unsaved_changes'):
                    self.editor.mark_unsaved_changes()

        except ImportError:
            self.log_error("Target range dialog not available")
        except Exception as e:
            self.log_error(f"Error opening movement range editor: {e}")

    def populate_data(self, data: Dict[str, Any]) -> None:
        """Populate the UI with data from an ability."""
        try:
            self.log_debug(f"Populating buff piece data: {data}")
            
            # Populate target selector
            target_selector = self.get_widget_by_name("buff_target_selector")
            if target_selector:
                buff_targets = data.get("buffTargets", data.get("buff_targets", []))
                if buff_targets:
                    target_selector.set_pieces(buff_targets)
            
            # Populate duration checkbox and spinner
            duration_check = self.get_widget_by_name("buff_duration_check")
            duration_spin = self.get_widget_by_name("buff_duration_spin")
            duration = data.get("buffDuration", 0)
            has_duration = duration > 0

            if duration_check:
                duration_check.setChecked(has_duration)

            if duration_spin:
                duration_spin.setValue(max(1, duration))
                duration_spin.setVisible(has_duration)

            # Populate buff options
            buff_options = data.get("buffOptions", {})

            movement_check = self.get_widget_by_name("buff_movement_check")
            if movement_check:
                is_checked = buff_options.get("movement", False)
                movement_check.setChecked(is_checked)
                # Show/hide range button
                movement_range_btn = self.get_widget_by_name("buff_movement_range_btn")
                if movement_range_btn:
                    movement_range_btn.setVisible(is_checked)

                # Load movement pattern if available
                movement_pattern = data.get("buffMovementPattern")
                if movement_pattern:
                    self.movement_range_pattern = movement_pattern

            ability_check = self.get_widget_by_name("buff_ability_check")
            if ability_check:
                is_checked = buff_options.get("abilities", False)
                ability_check.setChecked(is_checked)
                # Show/hide ability selector
                ability_selector = self.get_widget_by_name("buff_ability_selector")
                if ability_selector:
                    ability_selector.setVisible(is_checked)
                    # Populate abilities if present
                    abilities = data.get("buffAbilities", [])
                    if abilities:
                        ability_selector.set_abilities(abilities)
            
            self.log_debug("Buff piece data populated successfully")
            
        except Exception as e:
            self.log_error(f"Error populating buff piece data: {e}")
    
    def collect_data(self) -> Dict[str, Any]:
        """Collect data from the UI widgets."""
        try:
            data = {}
            
            # Collect target pieces
            target_selector = self.get_widget_by_name("buff_target_selector")
            if target_selector:
                pieces = target_selector.get_pieces()
                if pieces:
                    data["buffTargets"] = pieces
            
            # Collect duration
            duration_check = self.get_widget_by_name("buff_duration_check")
            duration_spin = self.get_widget_by_name("buff_duration_spin")
            if duration_check and duration_check.isChecked() and duration_spin:
                data["buffDuration"] = duration_spin.value()
            else:
                data["buffDuration"] = 0  # Indefinite/ability-defined

            # Collect buff options
            buff_options = {}

            movement_check = self.get_widget_by_name("buff_movement_check")
            if movement_check and movement_check.isChecked():
                buff_options["movement"] = True
                # Collect movement pattern if available
                if hasattr(self, 'movement_range_pattern'):
                    data["buffMovementPattern"] = self.movement_range_pattern

            ability_check = self.get_widget_by_name("buff_ability_check")
            if ability_check and ability_check.isChecked():
                buff_options["abilities"] = True
                # Collect abilities from selector
                ability_selector = self.get_widget_by_name("buff_ability_selector")
                if ability_selector:
                    abilities = ability_selector.get_abilities()
                    if abilities:
                        data["buffAbilities"] = abilities
            
            if buff_options:
                data["buffOptions"] = buff_options
            
            self.log_debug(f"Collected buff piece data: {data}")
            return data
            
        except Exception as e:
            self.log_error(f"Error collecting buff piece data: {e}")
            return {}
