#!/usr/bin/env python3
"""
Unified Theme System for Adventure Chess Creator

This module consolidates all theming functionality from theme.py, theme_manager.py,
and theme_utils.py into a single comprehensive system. Provides both manual and
automatic theming capabilities with backward compatibility.

Usage:
    from core.ui.unified_theme import UnifiedTheme
    
    # Apply themes to widgets
    UnifiedTheme.apply_theme(widget, 'primary_button')
    
    # Create themed widgets
    button = UnifiedTheme.create_themed_button("Save", 'primary_button')
    
    # Get colors and styles
    color = UnifiedTheme.get_color('primary')
    style = UnifiedTheme.get_style('main_window')
    
    # Manual theme application
    UnifiedTheme.apply_manual_themes(container)
"""

import functools
import re
from typing import Dict, List, Optional, Any, Union, Callable
from contextlib import contextmanager
from PyQt6.QtWidgets import QWidget, QPushButton, QLabel, QLineEdit, QTextEdit, QComboBox, QGroupBox, QTabWidget
from PyQt6.QtCore import QObject


class UnifiedTheme:
    """
    Unified theme system consolidating all theming functionality
    """
    
    # ========== THEME COLORS ==========
    
    COLORS = {
        # Primary colors
        'primary': '#4a90e2',
        'primary_hover': '#5599dd',
        'primary_pressed': '#2255aa',
        
        # Secondary colors
        'secondary': '#6a1b9a',
        'secondary_hover': '#7e37bf',
        
        # Status colors
        'success': '#4caf50',
        'warning': '#ff9800',
        'danger': '#f44336',
        
        # Background colors
        'background_primary': '#1a202c',
        'background_secondary': '#2d3748',
        'surface': '#4a5568',
        
        # Text colors
        'text_primary': '#ffffff',
        'text_secondary': '#e2e8f0',
        'text_muted': '#718096',
        
        # Border colors
        'border': '#4a5568',
        'border_focus': '#66aaff',
        
        # Chess board colors
        'chess_light': '#f0d9b5',
        'chess_dark': '#b58863',
        'chess_border': '#8b4513',
    }
    
    # ========== THEME STYLES ==========
    
    STYLES = {
        'primary_button': f"""
            QPushButton {{
                background-color: {COLORS['primary']};
                color: {COLORS['text_primary']};
                border: 1px solid {COLORS['border']};
                border-radius: 4px;
                padding: 8px 16px;
                font-weight: bold;
            }}
            QPushButton:hover {{
                background-color: {COLORS['primary_hover']};
            }}
            QPushButton:pressed {{
                background-color: {COLORS['primary_pressed']};
            }}
        """,
        
        'secondary_button': f"""
            QPushButton {{
                background-color: {COLORS['secondary']};
                color: {COLORS['text_primary']};
                border: 1px solid {COLORS['border']};
                border-radius: 4px;
                padding: 8px 16px;
            }}
            QPushButton:hover {{
                background-color: {COLORS['secondary_hover']};
            }}
        """,
        
        'success_button': f"""
            QPushButton {{
                background-color: {COLORS['success']};
                color: {COLORS['text_primary']};
                border: 1px solid {COLORS['border']};
                border-radius: 4px;
                padding: 8px 16px;
            }}
        """,
        
        'danger_button': f"""
            QPushButton {{
                background-color: {COLORS['danger']};
                color: {COLORS['text_primary']};
                border: 1px solid {COLORS['border']};
                border-radius: 4px;
                padding: 8px 16px;
            }}
        """,
        
        'main_window': f"""
            QMainWindow {{
                background-color: {COLORS['background_primary']};
                color: {COLORS['text_primary']};
            }}
        """,
        
        'dialog': f"""
            QDialog {{
                background-color: {COLORS['background_secondary']};
                color: {COLORS['text_primary']};
            }}
        """,
        
        'line_edit': f"""
            QLineEdit {{
                background-color: {COLORS['surface']};
                color: {COLORS['text_primary']};
                border: 1px solid {COLORS['border']};
                border-radius: 4px;
                padding: 4px 8px;
            }}
            QLineEdit:focus {{
                border-color: {COLORS['border_focus']};
            }}
        """,
        
        'combo_box': f"""
            QComboBox {{
                background-color: {COLORS['surface']};
                color: {COLORS['text_primary']};
                border: 1px solid {COLORS['border']};
                border-radius: 4px;
                padding: 4px 8px;
            }}
        """,
        
        'group_box': f"""
            QGroupBox {{
                color: {COLORS['text_primary']};
                border: 1px solid {COLORS['border']};
                border-radius: 4px;
                margin-top: 8px;
                padding-top: 8px;
            }}
            QGroupBox::title {{
                subcontrol-origin: margin;
                left: 8px;
                padding: 0 4px 0 4px;
            }}
        """,
    }
    
    # ========== MANUAL THEME ASSIGNMENTS ==========
    
    MANUAL_THEME_ASSIGNMENTS = {
        # Action buttons
        'save_buttons': 'primary_button',
        'apply_buttons': 'primary_button', 
        'ok_buttons': 'primary_button',
        'submit_buttons': 'primary_button',
        'confirm_buttons': 'primary_button',
        
        # Creation buttons
        'new_buttons': 'success_button',
        'add_buttons': 'success_button',
        'create_buttons': 'success_button',
        'insert_buttons': 'success_button',
        
        # Destructive buttons
        'delete_buttons': 'danger_button',
        'remove_buttons': 'danger_button',
        'clear_buttons': 'danger_button',
        'reset_buttons': 'danger_button',
        
        # Navigation buttons
        'cancel_buttons': 'secondary_button',
        'close_buttons': 'secondary_button',
        'back_buttons': 'secondary_button',
        'next_buttons': 'secondary_button',
    }
    
    # ========== CORE METHODS ==========
    
    @classmethod
    def get_color(cls, color_name: str) -> str:
        """Get a color value by name"""
        return cls.COLORS.get(color_name, '#ffffff')
    
    @classmethod
    def get_style(cls, style_name: str) -> str:
        """Get a style definition by name"""
        return cls.STYLES.get(style_name, '')
    
    @classmethod
    def apply_theme_to_widget(cls, widget: QWidget, style_name: str) -> None:
        """Apply a theme style to a widget"""
        style = cls.get_style(style_name)
        if style:
            widget.setStyleSheet(style)
    
    @classmethod
    def apply_window_theme(cls, window: QWidget) -> None:
        """Apply the main window theme"""
        cls.apply_theme_to_widget(window, 'main_window')
    
    @classmethod
    def apply_dialog_theme(cls, dialog: QWidget) -> None:
        """Apply the dialog theme"""
        cls.apply_theme_to_widget(dialog, 'dialog')
    
    # ========== WIDGET FACTORIES ==========
    
    @classmethod
    def create_themed_button(cls, text: str, style: str = 'secondary_button') -> QPushButton:
        """Create a themed button"""
        button = QPushButton(text)
        cls.apply_theme_to_widget(button, style)
        return button
    
    @classmethod
    def create_themed_line_edit(cls, placeholder: str = '') -> QLineEdit:
        """Create a themed line edit"""
        line_edit = QLineEdit()
        if placeholder:
            line_edit.setPlaceholderText(placeholder)
        cls.apply_theme_to_widget(line_edit, 'line_edit')
        return line_edit
    
    @classmethod
    def create_themed_combo_box(cls) -> QComboBox:
        """Create a themed combo box"""
        combo_box = QComboBox()
        cls.apply_theme_to_widget(combo_box, 'combo_box')
        return combo_box
    
    @classmethod
    def create_themed_group_box(cls, title: str) -> QGroupBox:
        """Create a themed group box"""
        group_box = QGroupBox(title)
        cls.apply_theme_to_widget(group_box, 'group_box')
        return group_box
    
    @classmethod
    def create_themed_label(cls, text: str) -> QLabel:
        """Create a themed label"""
        label = QLabel(text)
        label.setStyleSheet(f"color: {cls.COLORS['text_primary']};")
        return label
    
    # ========== MANUAL THEMING ==========
    
    @classmethod
    def apply_manual_themes(cls, container: QWidget, theme_assignments: Optional[Dict[str, str]] = None) -> None:
        """
        Apply manual themes to a container
        
        Args:
            container: Container widget to theme
            theme_assignments: Optional custom theme assignments (widget_name -> theme_name)
        """
        assignments = theme_assignments or cls.MANUAL_THEME_ASSIGNMENTS
        
        # Apply themes based on widget object names and types
        for widget in container.findChildren(QWidget):
            widget_name = widget.objectName().lower()
            widget_type = type(widget).__name__.lower()
            
            # Check for specific assignments
            for pattern, theme in assignments.items():
                if pattern in widget_name or pattern.replace('_', '') in widget_name:
                    cls.apply_theme_to_widget(widget, theme)
                    break
            else:
                # Apply default themes based on widget type
                if isinstance(widget, QPushButton):
                    cls.apply_theme_to_widget(widget, 'secondary_button')
                elif isinstance(widget, QLineEdit):
                    cls.apply_theme_to_widget(widget, 'line_edit')
                elif isinstance(widget, QComboBox):
                    cls.apply_theme_to_widget(widget, 'combo_box')
                elif isinstance(widget, QGroupBox):
                    cls.apply_theme_to_widget(widget, 'group_box')


# ========== BACKWARD COMPATIBILITY ALIASES ==========

# For theme.py compatibility
get_color = UnifiedTheme.get_color
get_style = UnifiedTheme.get_style
apply_theme_to_widget = UnifiedTheme.apply_theme_to_widget
apply_window_theme = UnifiedTheme.apply_window_theme
apply_dialog_theme = UnifiedTheme.apply_dialog_theme
create_themed_button = UnifiedTheme.create_themed_button
create_themed_line_edit = UnifiedTheme.create_themed_line_edit
create_themed_combo_box = UnifiedTheme.create_themed_combo_box
create_themed_group_box = UnifiedTheme.create_themed_group_box
create_themed_label = UnifiedTheme.create_themed_label

# For theme_manager.py compatibility
def get_themed_style(style_name):
    """Get themed style (backward compatibility)"""
    return UnifiedTheme.get_style(style_name)

def apply_theme(widget, style_name):
    """Apply theme (backward compatibility)"""
    return UnifiedTheme.apply_theme_to_widget(widget, style_name)

class ThemeManager:
    """Legacy theme manager - now redirects to unified theme"""
    
    @staticmethod
    def get_style(style_name):
        return UnifiedTheme.get_style(style_name)
    
    @staticmethod
    def apply_window_theme(window):
        return UnifiedTheme.apply_window_theme(window)
    
    @staticmethod
    def get_color(color_name):
        return UnifiedTheme.get_color(color_name)
    
    @staticmethod
    def set_theme(theme):
        """Theme setting is no longer needed - always dark theme"""
        pass

# For theme_utils.py compatibility
apply_manual_themes = UnifiedTheme.apply_manual_themes

# Additional compatibility functions for missing imports
def create_themed_dialog_buttons():
    """Create standard dialog buttons"""
    return [
        UnifiedTheme.create_themed_button("OK", 'primary_button'),
        UnifiedTheme.create_themed_button("Cancel", 'secondary_button')
    ]

def create_manual_themed_form():
    """Create a manually themed form - placeholder for compatibility"""
    pass

def manual_theme_context():
    """Manual theme context - placeholder for compatibility"""
    pass

def apply_adventure_chess_theme(widget):
    """Apply Adventure Chess specific theming"""
    UnifiedTheme.apply_manual_themes(widget)

def themed_widget(theme_name):
    """Decorator for themed widgets"""
    def decorator(func):
        def wrapper(*args, **kwargs):
            widget = func(*args, **kwargs)
            UnifiedTheme.apply_theme_to_widget(widget, theme_name)
            return widget
        return wrapper
    return decorator

def auto_theme_widget(widget):
    """Auto theme a single widget"""
    UnifiedTheme.apply_manual_themes(widget)

def auto_theme_widget_children(container):
    """Auto theme all children of a container"""
    UnifiedTheme.apply_manual_themes(container)

def theme_context(container):
    """Theme context manager"""
    class ThemeContext:
        def __enter__(self):
            return self
        def __exit__(self, exc_type, exc_val, exc_tb):
            UnifiedTheme.apply_manual_themes(container)
    return ThemeContext()

def get_responsive_spacing():
    """Get responsive spacing values"""
    return {'small': 4, 'medium': 8, 'large': 16}

def migrate_existing_styles():
    """Migrate existing styles - placeholder for compatibility"""
    pass

# Compatibility classes
class ThemeAwareWidget:
    """Theme aware widget base class"""
    pass

class ManualThemeUtils:
    """Manual theme utilities - alias for UnifiedTheme"""
    @staticmethod
    def apply_manual_themes(container, assignments=None):
        return UnifiedTheme.apply_manual_themes(container, assignments)
