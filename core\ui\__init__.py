"""
Core UI Module for Adventure Chess Creator

This module contains core UI components and visual feedback systems.
Consolidated from the enhancements/ui/ folder and ui/ directory for better organization.
"""

from .adjacency_preview import AdjacencyPreviewWidget

# Visual feedback components (from enhancements)
from .color_schemes import ColorSchemes
from .file_operations import FileOperationsWidget

# Unified theme management system
from .unified_theme import (
    UnifiedTheme,
    ThemeManager,
    apply_theme_to_widget,
    get_themed_style,
    get_color,
    apply_window_theme,
    apply_dialog_theme,
    create_themed_button,
    create_themed_group_box,
    create_themed_line_edit,
    create_themed_combo_box,
    create_themed_label,
    apply_manual_themes,
    # Compatibility functions
    create_themed_dialog_buttons,
    create_manual_themed_form,
    manual_theme_context,
    themed_widget,
    auto_theme_widget,
    auto_theme_widget_children,
    theme_context,
    ThemeAwareWidget,
    apply_adventure_chess_theme,
    get_responsive_spacing,
    migrate_existing_styles,
    ManualThemeUtils,
)

# UI components (from ui/ directory)
# Note: GridToggleWidget and AreaEffectGridWidget have been replaced by EnhancedChessBoardWidget
from .enhanced_chess_board import EnhancedChessBoardWidget
from .chess_board_core import ChessBoardCore, BoardMode, TileState
from .grid_visualization import EnhancedGridVisualization, GridPatternEditor
# Inline selection widgets moved to dialogs folder for better organization
# Import directly from dialogs.inline_selectors when needed to avoid circular imports
from .loading_indicators import EnhancedLoadingIndicator, OperationFeedbackManager
from .range_preview import RangePreviewWidget
from .status_display import ValidationStatusWidget

# UI components (from enhancements integration)
# Search components removed - were unused in the application

# Validation components (from validation module)
from ..validation.validation_rules import (
    EnhancedValidationMixin,
    ValidationRules,
    create_ability_validation_rules,
    create_piece_validation_rules,
)
# Consolidated UI utilities (backward compatibility maintained)
from .consolidated_ui_utils import (
    UIUtils,
    ResponsiveScrollArea,
    create_section_header,
    create_info_box,
    create_legend_item,
    create_dialog_buttons,
    create_grid_instructions,
    make_widget_responsive,
    create_scrollable_content,
    optimize_layout_for_small_screens,
    create_responsive_scroll_area,
    create_responsive_layout,
    create_responsive_splitter,
)
# Legacy imports for backward compatibility
from .ui_utilities import *
from .ui_utils import *
from .visual_feedback_integration import (
    VisualFeedbackIntegrator,
    VisualFeedbackManager,
    apply_visual_feedback_to_editor,
    apply_visual_feedback_to_main_window,
    get_visual_feedback_manager,
)

__all__ = [
    # Visual feedback components
    "ColorSchemes",
    "EnhancedLoadingIndicator",
    "OperationFeedbackManager",
    "EnhancedGridVisualization",
    "GridPatternEditor",
    "VisualFeedbackIntegrator",
    "VisualFeedbackManager",
    "get_visual_feedback_manager",
    "apply_visual_feedback_to_editor",
    "apply_visual_feedback_to_main_window",
    # Enhanced Chess Board Widget
    "EnhancedChessBoardWidget",
    "ChessBoardCore",
    "BoardMode",
    "TileState",
    # Grid components (replaced by EnhancedChessBoardWidget)
    # "GridToggleWidget",  # Deprecated - use EnhancedChessBoardWidget
    # "AreaEffectGridWidget",  # Deprecated - use EnhancedChessBoardWidget
    # File operations
    "FileOperationsWidget",
    # Status display
    "ValidationStatusWidget",
    # Utility functions
    "create_section_header",
    "create_info_box",
    "create_legend_item",
    "create_dialog_buttons",
    "create_grid_instructions",
    # Inline selection widgets (moved to dialogs.inline_selectors)
    # "InlinePieceSelector",  # Import from dialogs.inline_selectors
    # "InlineAbilitySelector",  # Import from dialogs.inline_selectors
    # Adjacency preview
    "AdjacencyPreviewWidget",
    "RangePreviewWidget",
    # Responsive UI utilities
    "ResponsiveScrollArea",
    "ResponsiveWidget",
    "ResponsiveLayout",
    "ResponsiveSplitter",
    "TabWidgetResponsive",
    "setup_responsive_window",
    "get_screen_size_category",
    "setup_responsive_window_with_fallback",
    "make_widget_responsive",
    "create_scrollable_content",
    "optimize_layout_for_small_screens",
    # Search components removed - were unused in the application
    # UI Utilities (consolidated)
    "UIUtils",
    # Validation components
    "ValidationRules",
    "EnhancedValidationMixin",
    "create_piece_validation_rules",
    "create_ability_validation_rules",
    # Theme system (consolidated)
    "UnifiedTheme",
    "ThemeManager",
    "apply_theme_to_widget",
    "get_themed_style",
    "get_color",
    "apply_window_theme",
    "apply_dialog_theme",
    "create_themed_button",
    "create_themed_group_box",
    "create_themed_line_edit",
    "create_themed_combo_box",
    "create_themed_label",
    "ManualThemeUtils",
    "apply_manual_themes",
    "create_themed_dialog_buttons",
    "create_manual_themed_form",
    "manual_theme_context",
    "themed_widget",
    "auto_theme_widget",
    "auto_theme_widget_children",
    "theme_context",
    "ThemeAwareWidget",
    "apply_adventure_chess_theme",
    "get_responsive_spacing",
    "migrate_existing_styles",
]
