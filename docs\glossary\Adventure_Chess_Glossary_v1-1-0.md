# Adventure Chess Glossary v1.1.0

🚀 **COMPREHENSIVE ARCHITECTURE MODERNIZATION** - Complete codebase restructuring with enhanced modularity and performance
🔧 **ADVANCED COMPONENT INTEGRATION** - Unified core module system with specialized handlers and managers
📱 **ENHANCED UI FRAMEWORK** - Professional-grade interface components with responsive design and dark theme
✅ **PRODUCTION-READY VALIDATION** - Comprehensive Pydantic schema system with flexible data handling
📚 **COMPLETE DOCUMENTATION OVERHAUL** - Detailed technical documentation with developer onboarding guides

> **📚 HISTORICAL PRESERVATION NOTE**: This glossary is part of a versioned documentation system that intentionally preserves all versions as a historical record of the project's evolution. While the codebase undergoes cleanup and consolidation, the glossary versions serve as an archive and should be maintained for reference.

## Table of Contents
1. [Quick Reference Index](#quick-reference-index)
2. [Version 1.1.0 Improvements](#version-110-improvements)
3. [Architecture Overview](#architecture-overview)
4. [Core Module System](#core-module-system)
5. [Editor Components](#editor-components)
6. [Data Management](#data-management)
7. [UI Framework](#ui-framework)
8. [Canonical Abilities Reference](#canonical-abilities-reference)
9. [Configuration Options Reference](#configuration-options-reference)
10. [Development Guidelines](#development-guidelines)
11. [Testing Framework](#testing-framework)
12. [Troubleshooting](#troubleshooting)
13. [Version History](#version-history)

---

## Quick Reference Index

### Application Essentials
- **Adventure Chess Creator**: Production-ready desktop application for creating custom chess variants with professional UI and comprehensive validation
- **Main Entry Point**: [main.py](main.py) - Application launcher with enhanced error handling and responsive window management
- **Core Architecture**: Modular [core/](core/) directory system with specialized subsystems for data, UI, performance, and security
- **Editor System**: Refactored [piece editor](editors/piece_editor/piece_editor_main.py) and [ability editor](editors/ability_editor/ability_editor_main.py) with component-based architecture
- **Data Storage**: JSON files with [Pydantic validation](schemas/), automatic backup, and readable formatting

### NEW in v1.1.0 - Architecture Modernization
- **Core Module System**: Complete [core/](core/) directory restructuring with 10 specialized subsystems
- **Component-Based Editors**: Refactored editors with separated data handlers, UI components, and specialized managers
- **Enhanced Schema System**: Flexible [Pydantic schemas](schemas/) with legacy compatibility and permissive validation
- **Professional UI Framework**: Dark theme integration, responsive design, and enhanced preview components
- **Advanced Error Handling**: User-friendly error system with comprehensive logging and recovery mechanisms
- **Performance Optimization**: Lazy loading, caching systems, and file system optimization
- **Security Framework**: Data validation, secure file operations, and crash recovery systems

### Technical Foundation
- **Base Classes**: Abstract foundation classes in [core/base_classes/](core/base_classes/) for consistent component development
- **Data Handlers**: Specialized handlers in [core/handlers/](core/handlers/) for piece and ability data management
- **UI Components**: Professional UI toolkit in [core/ui/](core/ui/) with theme management and responsive design
- **Validation System**: Comprehensive validation in [core/validation/](core/validation/) with real-time feedback
- **Performance Systems**: Optimization tools in [core/performance/](core/performance/) for enhanced application speed

---

## Version 1.1.0 Improvements

### **Complete Architecture Modernization**

#### **Core Module System Implementation**
- **Modular Design**: Complete restructuring into 10 specialized core subsystems
- **Base Classes**: Abstract foundation classes for editors, widgets, managers, and data handlers
- **Interface Definitions**: Standardized interfaces for data handling and UI interaction
- **Component Separation**: Clear separation of concerns with focused, testable modules

#### **Enhanced Editor Architecture**
- **Component-Based Design**: Editors split into specialized components (data handlers, UI components, managers)
- **Piece Editor Refactoring**: [PieceEditorWindow](editors/piece_editor/piece_editor_main.py) with movement, promotion, and icon managers
- **Ability Editor Refactoring**: [AbilityEditorWindow](editors/ability_editor/ability_editor_main.py) with tag management and UI components
- **Backward Compatibility**: Maintained existing interfaces while modernizing internal architecture

#### **Advanced Data Management**
- **Pydantic Schema System**: Comprehensive [schemas](schemas/) with flexible validation and legacy support
- **Data Handler Architecture**: Specialized handlers for piece and ability data with validation and error handling
- **Single Source of Truth**: Centralized data management preventing inconsistencies
- **Migration Support**: Automatic data format upgrades with backward compatibility

### **Professional UI Framework**

#### **Enhanced Interface Components**
- **Dark Theme Integration**: Comprehensive dark theme with professional styling
- **Responsive Design**: Adaptive layouts with small screen fallback support
- **Preview Components**: Advanced chess board visualizations with interactive patterns
- **Status Systems**: Real-time feedback with success/error notifications

#### **Advanced Dialog System**
- **Range Preview Widget**: Professional 8x8 chess board with interactive pattern editing
- **Adjacency Preview**: Enhanced adjacency pattern visualization with distance highlighting
- **Pattern Editors**: Comprehensive pattern editing with preset buttons and real-time preview
- **Unified Dialogs**: Consistent dialog system with enhanced user experience

### **Performance and Security Enhancements**

#### **Performance Optimization**
- **Lazy Loading System**: Deferred loading of expensive operations for faster startup
- **Caching Framework**: Intelligent caching of frequently accessed data
- **File System Optimization**: Enhanced file operations with indexing and search optimization
- **Memory Management**: Proper cleanup and garbage collection patterns

#### **Security and Reliability**
- **Data Validation**: Comprehensive input validation with security checks
- **Error Boundaries**: Robust error handling with user-friendly messages
- **Crash Recovery**: Automatic backup and recovery systems
- **Secure Operations**: Protected file operations with validation

---

## Architecture Overview

### **Directory Structure**
```
Adventure Chess Creator/
├── core/                    # Core module system (10 subsystems)
│   ├── base_classes/        # Abstract foundation classes
│   ├── handlers/           # Specialized data handlers
│   ├── managers/           # Component managers
│   ├── interfaces/         # Interface definitions
│   ├── ui/                 # UI components and utilities
│   ├── error_handling/     # Error management system
│   ├── performance/        # Performance optimization
│   ├── security/           # Security and validation
│   ├── validation/         # Data validation rules
│   └── workflow/           # Template and workflow systems
├── editors/                # Editor applications
│   ├── piece_editor/       # Piece editor components
│   └── ability_editor/     # Ability editor components
├── schemas/                # Pydantic data models
├── dialogs/                # Dialog components
├── data/                   # JSON data files
├── utils/                  # Utility functions
└── tests/                  # Comprehensive test suite
```

### **Design Patterns**
- **Single Source of Truth**: Centralized data management through specialized handlers
- **Component Architecture**: Modular design with clear separation of concerns
- **Lazy Loading**: Performance optimization through deferred initialization
- **Error Boundaries**: Comprehensive error handling at all architectural levels
- **Interface Segregation**: Focused interfaces for specific functionality

---

## Core Module System

### **Base Classes** (`core/base_classes/`)
- **[BaseEditor](core/base_classes/base_editor.py)**: Foundation for all editor windows with standardized data operations
- **[BaseDataHandler](core/base_classes/base_data_handler.py)**: Standardized data management with file operations and validation
- **[BaseManager](core/base_classes/base_manager.py)**: Core management functionality for application components
- **[BaseWidget](core/base_classes/base_widgets.py)**: Base UI widget classes with consistent behavior patterns

### **Data Handlers** (`core/handlers/`)
- **[EnhancedPieceDataHandler](core/handlers/specialized_data_handlers.py)**: Advanced piece data management with validation
- **[EnhancedAbilityDataHandler](core/handlers/specialized_data_handlers.py)**: Advanced ability data management with validation
- **Single Source of Truth**: All data operations go through centralized handlers

### **UI Components** (`core/ui/`)
- **[InlineSelectionWidgets](core/ui/inline_selection_widgets.py)**: Reusable selection components for pieces and abilities
- **[ThemeManager](core/ui/theme_manager.py)**: Dark theme management with professional styling
- **[RangePreviewWidget](core/ui/range_preview.py)**: Interactive 8x8 chess board for pattern editing
- **[AdjacencyPreviewWidget](core/ui/adjacency_preview.py)**: Enhanced adjacency pattern visualization

### **Error Handling** (`core/error_handling/`)
- **[EnhancedErrorHandler](core/error_handling/error_handling_system.py)**: Comprehensive error management
- **[UserFriendlyErrorDialog](core/error_handling/error_handling_system.py)**: User-facing error presentation
- **[ErrorMessageTranslator](core/error_handling/error_handling_system.py)**: Technical error to user message translation

### **Performance** (`core/performance/`)
- **[LazyDataManager](core/performance/lazy_loading_system.py)**: Lazy loading for improved startup performance
- **[FileSystemOptimizer](core/performance/file_system_optimization.py)**: File indexing and search optimization
- **[PerformanceMonitor](core/performance/performance_monitor.py)**: Performance monitoring and optimization

---

## Editor Components

### **Piece Editor** (`editors/piece_editor/`)
- **[PieceEditorWindow](editors/piece_editor/piece_editor_main.py)**: Main coordinator integrating all piece editor components
- **[PieceDataHandler](editors/piece_editor/piece_data_handlers.py)**: Specialized piece data management
- **[PieceUIComponents](editors/piece_editor/piece_ui_components.py)**: UI creation and layout management
- **[PieceMovementManager](editors/piece_editor/piece_movement_manager.py)**: Movement pattern management
- **[PiecePromotionManager](editors/piece_editor/piece_promotion_manager.py)**: Promotion system management
- **[PieceIconManager](editors/piece_editor/piece_icon_manager.py)**: Icon selection and preview management

### **Ability Editor** (`editors/ability_editor/`)
- **[AbilityEditorWindow](editors/ability_editor/ability_editor_main.py)**: Main coordinator for ability editor components
- **[AbilityDataHandler](editors/ability_editor/ability_data_handlers.py)**: Specialized ability data management
- **[AbilityUIComponents](editors/ability_editor/ability_ui_components.py)**: UI creation and layout management
- **[AbilityTagManager](editors/ability_editor/ability_tag_managers.py)**: Tag selection and configuration management

---

## Data Management

### **Pydantic Schema System** (`schemas/`)
- **[Piece Schema](schemas/piece_schema.py)**: Comprehensive piece data validation with movement patterns and abilities
- **[Ability Schema](schemas/ability_schema.py)**: Flexible ability validation supporting all canonical tags
- **[Base Schema](schemas/base.py)**: Common data types and validation utilities
- **Legacy Compatibility**: Automatic conversion between old and new data formats

### **Data Validation Features**
- **Permissive Mode**: Flexible validation allowing maximum user creativity
- **Real-time Validation**: Immediate feedback on data entry
- **Error Recovery**: Automatic correction of common data issues
- **Migration Support**: Seamless upgrades from older data formats

### **File Operations**
- **Automatic Backup**: Secure backup before any file modifications
- **Readable Formatting**: JSON files formatted for human readability
- **Error Handling**: Comprehensive error recovery for file operations
- **Concurrent Access**: Safe handling of multiple file operations

---

## UI Framework

### **Theme System**
- **Dark Theme**: Professional dark theme with consistent styling across all components
- **Responsive Design**: Adaptive layouts supporting various screen sizes
- **Color Schemes**: Carefully designed color palette for optimal user experience
- **Accessibility**: High contrast and readable interface elements

### **Interactive Components**
- **Chess Board Previews**: Interactive 8x8 grids for pattern visualization
- **Pattern Editors**: Advanced pattern editing with preset buttons and real-time feedback
- **Selection Widgets**: Inline piece and ability selectors with preview capabilities
- **Status Indicators**: Real-time feedback with success/error notifications

### **Dialog System**
- **Range Editor Dialog**: Professional pattern editing with chess board visualization
- **Adjacency Dialog**: Enhanced adjacency configuration with distance-based highlighting
- **Pattern Management**: Advanced pattern storage and retrieval systems
- **Unified Interface**: Consistent dialog design across all components

---

## Canonical Abilities Reference

### **Action Tags**
- **move** (range_editor): Teleports piece to target square within range
- **summon** (custom_widget): Creates new pieces at target locations with piece selection
- **revival** (custom_widget): Resurrects destroyed pieces with configuration options
- **capture** (dropdown): Destroys pieces at target locations with target type selection
- **carryPiece** (custom_widget): Allows piece to carry other pieces with range and drop configuration
- **swapPlaces** (custom_widget): Exchanges positions with target piece using piece selection
- **displacePiece** (dropdown + spinner): Pushes target piece in specified direction and distance
- **immobilize** (spinner): Prevents piece movement for specified turns
- **convertPiece** (custom_widget): Changes target pieces into different piece types
- **duplicate** (custom_widget): Creates copies of piece at offset positions
- **buffPiece** (custom_widget): Temporarily enhances target pieces with ability additions
- **debuffPiece** (custom_widget): Temporarily weakens target pieces with ability restrictions
- **addObstacle** (dropdown): Places obstacles on target squares with type selection
- **removeObstacle** (dropdown): Removes obstacles from target squares with type selection
- **trapTile** (custom_widget): Creates hidden traps on target squares with effect configuration

### **Targeting Tags**
- **range** (range_editor): Defines targeting area using 8x8 interactive grid
- **areaEffect** (dropdown + spinner): Affects multiple squares around target with shape and size options

### **Condition Tags**
- **adjacencyRequired** (custom_widget): Ability only works when adjacent to specific pieces
- **losRequired** (checkbox): Requires clear line of sight to target with ignore options
- **noTurnCost** (spinner): Ability doesn't consume turn points with usage limit
- **delay** (dropdown + spinner): Ability effect occurs after specified turns or actions

### **Special Tags**
- **shareSpace** (spinner + checkboxes): Multiple pieces can occupy same square with restrictions
- **passThrough** (custom_widget): Can target through other pieces with capture and piece selection
- **pulseEffect** (spinner): Repeating effect that triggers every N turns
- **fogOfWar** (dropdown + custom_widget): Reveals hidden areas with vision type and range configuration
- **invisible** (custom_widget): Makes piece undetectable with reveal conditions
- **reaction** (dropdown + custom_widget): Triggers automatically in response to events
- **requiresStartingPosition** (checkbox): Ability only works if piece hasn't moved from starting position

---

## Configuration Options Reference

### **Piece Configuration**
- **Basic Information**
  - `name` (text_input): Piece name (required, 1-50 characters)
  - `description` (text_area): Piece description (optional, max 500 characters)
  - `role` (dropdown): Commander/Supporter role selection

- **Movement System**
  - `movement.type` (dropdown): orthogonal/diagonal/lShape/any/custom movement types
  - `movement.pattern` (pattern_editor): Custom 8x8 movement pattern for custom type
  - `movement.piece_position` (coordinate): Piece position in custom pattern [row, col]

- **Special Properties**
  - `can_castle` (checkbox): Can participate in castling
  - `can_capture` (checkbox): Can capture other pieces
  - `color_directional` (checkbox): Behavior differs by color
  - `track_starting_position` (checkbox): Track starting position for abilities

- **Point System** (enable_recharge checkbox reveals)
  - `max_points` (spinner): Maximum action points (0-100)
  - `starting_points` (spinner): Starting action points (0-max_points)
  - `recharge_type` (dropdown): turnRecharge/adjacencyRecharge/committedRecharge
    - `turn_points` (spinner): Points gained per turn (when turnRecharge)
    - `adjacency_recharge_config` (custom_widget): Adjacency configuration (when adjacencyRecharge)
    - `committed_recharge_turns` (spinner): Turns for committed recharge (when committedRecharge)

- **Icons**
  - `black_icon` (file_selector): Black piece icon filename
  - `white_icon` (file_selector): White piece icon filename

- **Abilities and Promotions**
  - `abilities` (custom_widget): List of ability filenames with inline selector
  - `promotions` (custom_widget): Primary promotion options with piece selection
  - `secondary_promotions` (custom_widget): Secondary promotion options with piece selection

### **Ability Configuration**
- **Basic Information**
  - `name` (text_input): Ability name (required, 1-50 characters)
  - `description` (text_area): Ability description (optional, max 500 characters)
  - `cost` (spinner): Point cost for using ability (0-100)
  - `activation_mode` (dropdown): auto/click activation mode

- **Tag System**
  - `tags` (checkbox_list): Selection of canonical ability tags
  - Tag-specific configuration revealed based on selected tags

- **Common Range Configuration** (when range tag selected)
  - `range_mask` (range_editor): 8x8 targeting pattern
  - `piece_position` (coordinate): Piece position in range pattern [row, col]
  - `range_friendly_only` (checkbox): Range only affects friendly pieces
  - `range_enemy_only` (checkbox): Range only affects enemy pieces
  - `range_include_start` (checkbox): Include starting square in range
  - `range_include_self` (checkbox): Include self in range

---

## Development Guidelines

### **Adding New Features**
1. **Identify Subsystem**: Determine appropriate core subsystem (handlers, ui, validation, etc.)
2. **Extend Base Classes**: Use abstract base classes for consistent implementation
3. **Follow Patterns**: Implement established patterns for data handling and UI
4. **Add Validation**: Include comprehensive validation using Pydantic schemas
5. **Write Tests**: Create unit and integration tests for new functionality

### **Modifying Existing Components**
1. **Check Dependencies**: Understand component relationships before changes
2. **Maintain Interfaces**: Preserve existing interfaces for backward compatibility
3. **Update Documentation**: Keep documentation current with changes
4. **Test Thoroughly**: Run comprehensive test suite to ensure no regressions

### **Best Practices**
- **Single Responsibility**: Each component should have one clear purpose
- **Error Handling**: Implement comprehensive error boundaries
- **Performance**: Use lazy loading and caching for expensive operations
- **Security**: Validate all user input and file operations
- **Documentation**: Include comprehensive docstrings and comments

---

## Testing Framework

### **Test Categories**
- **[Unit Tests](tests/test_pydantic_models.py)**: Individual component testing with mock data
- **[Integration Tests](tests/test_main_app_integration.py)**: Component interaction testing
- **[UI Tests](tests/test_ui_automation.py)**: User interface automation testing
- **[Performance Tests](tests/test_performance_optimization.py)**: Performance and optimization validation
- **[Stress Tests](tests/test_application_stress.py)**: Application stability under load

### **Testing Infrastructure**
- **[Test Runner](tests/run_testing_infrastructure_tests.py)**: Automated test execution
- **Mock Data**: Comprehensive test data for all scenarios
- **Error Simulation**: Testing error handling and recovery
- **Performance Monitoring**: Automated performance regression detection

---

## Troubleshooting

### **Common Issues**
1. **Import Errors**: Ensure all dependencies are properly installed and core module is accessible
2. **Data Loading Issues**: Check file permissions and data format compatibility
3. **UI Rendering Issues**: Verify PyQt6 installation and theme configuration
4. **Performance Issues**: Use performance monitoring tools and check lazy loading

### **Debug Mode**
```python
import logging
logging.getLogger().setLevel(logging.DEBUG)
```

### **Error Recovery**
- **Automatic Backup**: Files are automatically backed up before modifications
- **Data Migration**: Automatic upgrade of older data formats
- **Crash Recovery**: Application state recovery after unexpected shutdowns
- **Validation Recovery**: Automatic correction of common data issues

---

## Version History

### **v1.1.0 - Architecture Modernization Complete**
- **Core Module System**: Complete restructuring into 10 specialized subsystems
- **Component-Based Editors**: Refactored editors with separated concerns
- **Enhanced Schema System**: Flexible Pydantic validation with legacy compatibility
- **Professional UI Framework**: Dark theme, responsive design, enhanced previews
- **Performance Optimization**: Lazy loading, caching, file system optimization
- **Security Framework**: Comprehensive validation and error handling
- **Advanced Testing**: Comprehensive test suite with 10 test categories
- **Complete Documentation**: Developer guides and architectural documentation

### **v1.0.9 - Codebase Modernization Complete**
- **Critical Bug Fixes**: Infinite recursion prevention and application stability
- **Architecture Modernization**: Enhancement folder elimination and consolidation
- **Enhanced Dialog System**: Professional preview components with dark theme
- **Code Quality**: Debug cleanup, cache removal, comprehensive documentation
- **Data Format Improvements**: Readable movement patterns and UI refinements

---

*Adventure Chess Creator v1.1.0 - Architecture Modernization Complete*  
*Professional-grade application with modular architecture, comprehensive validation, and enhanced user experience*