"""
Base Tag Configuration for Adventure Chess Creator

This module provides the base class that all tag configurations should inherit from.
It defines the standard interface for creating UI widgets and handling data.
"""

import logging
from abc import ABC, abstractmethod
from typing import Dict, Any, Optional
from PyQt6.QtWidgets import (
    QWidget, QForm<PERSON>ayout, QVBoxLayout, QHBoxLayout, QGroupBox,
    QLabel, QSpinBox, QCheckBox, QComboBox, QPushButton, QFrame
)
from PyQt6.QtCore import Qt

logger = logging.getLogger(__name__)


class BaseTagConfig(ABC):
    """
    Base class for all tag configuration implementations.
    
    Each tag configuration should inherit from this class and implement
    the required methods for creating UI and handling data.
    """
    
    def __init__(self, editor_instance, tag_name: str):
        """
        Initialize the tag configuration.
        
        Args:
            editor_instance: The AbilityEditorWindow instance
            tag_name: The name of the tag this config handles
        """
        self.editor = editor_instance
        self.tag_name = tag_name
        self.widgets = {}  # Store widget references for data population
    
    @abstractmethod
    def create_ui(self, parent_layout) -> None:
        """
        Create the UI widgets for this tag configuration.
        
        Args:
            parent_layout: The layout to add widgets to
        """
        pass
    
    @abstractmethod
    def populate_data(self, data: Dict[str, Any]) -> None:
        """
        Populate the UI widgets with data from an ability.
        
        Args:
            data: Dictionary containing ability data
        """
        pass
    
    @abstractmethod
    def collect_data(self) -> Dict[str, Any]:
        """
        Collect data from the UI widgets.
        
        Returns:
            Dictionary containing the tag's data
        """
        pass
    
    def get_title(self) -> str:
        """
        Get the display title for this tag configuration.
        
        Returns:
            The title string with emoji and formatting
        """
        return f"🔧 {self.tag_name.title()} Configuration"
    
    def get_widget_by_name(self, widget_name: str) -> Optional[QWidget]:
        """
        Get a widget by its name.
        
        Args:
            widget_name: The name of the widget to retrieve
            
        Returns:
            The widget if found, None otherwise
        """
        return self.widgets.get(widget_name)
    
    def store_widget(self, widget_name: str, widget: QWidget) -> None:
        """
        Store a widget reference for later use.
        
        Args:
            widget_name: The name to store the widget under
            widget: The widget to store
        """
        self.widgets[widget_name] = widget
        
        # Also store on the editor for backward compatibility
        if hasattr(self.editor, 'setattr'):
            setattr(self.editor, widget_name, widget)
    
    def connect_change_signals(self, widget: QWidget = None) -> None:
        """
        Connect change signals from a widget to mark unsaved changes.

        Args:
            widget: The widget to connect signals for. If None, connects all stored widgets.
        """
        try:
            if widget is not None:
                # Connect signals for a specific widget
                self._connect_widget_signals(widget)
            else:
                # Connect signals for all stored widgets (legacy compatibility)
                for widget_name, stored_widget in self.widgets.items():
                    if stored_widget is not None:
                        self._connect_widget_signals(stored_widget)

        except Exception as e:
            logger.warning(f"Could not connect change signals: {e}")

    def _connect_widget_signals(self, widget: QWidget) -> None:
        """Helper method to connect signals for a specific widget."""
        try:
            if hasattr(self.editor, 'mark_unsaved_changes'):
                # Connect appropriate signals based on widget type
                widget_type = type(widget).__name__

                if widget_type in ['QLineEdit', 'QTextEdit']:
                    widget.textChanged.connect(self.editor.mark_unsaved_changes)
                elif widget_type in ['QSpinBox', 'QDoubleSpinBox']:
                    widget.valueChanged.connect(self.editor.mark_unsaved_changes)
                elif widget_type == 'QCheckBox':
                    widget.stateChanged.connect(self.editor.mark_unsaved_changes)
                elif widget_type == 'QComboBox':
                    widget.currentTextChanged.connect(self.editor.mark_unsaved_changes)
                # InlineSelectors have their own change handling

        except Exception as e:
            logger.warning(f"Could not connect change signals for {widget_type}: {e}")
    
    def create_form_layout(self) -> QFormLayout:
        """
        Create a standard form layout for this tag configuration.
        
        Returns:
            A QFormLayout ready for use
        """
        from core.ui.ui_utils import ResponsiveLayout
        return ResponsiveLayout.create_form_layout()
    
    def log_debug(self, message: str) -> None:
        """Log a debug message with tag context."""
        logger.debug(f"[{self.tag_name}] {message}")
    
    def log_error(self, message: str) -> None:
        """Log an error message with tag context."""
        logger.error(f"[{self.tag_name}] {message}")

    # ========== STANDARDIZED UI CREATION METHODS ==========

    def create_main_group(self, title: str = None) -> QGroupBox:
        """
        Create a standardized main group box for the tag configuration.

        Args:
            title: Custom title, defaults to get_title()

        Returns:
            Configured QGroupBox with standard styling
        """
        group = QGroupBox(title or self.get_title())
        group.setStyleSheet("""
            QGroupBox {
                font-weight: bold;
                border: 2px solid #cccccc;
                border-radius: 8px;
                margin-top: 1ex;
                padding-top: 10px;
            }
            QGroupBox::title {
                subcontrol-origin: margin;
                left: 10px;
                padding: 0 5px 0 5px;
            }
        """)
        return group

    def create_description_label(self, text: str) -> QLabel:
        """
        Create a standardized description label.

        Args:
            text: Description text

        Returns:
            Configured QLabel with standard styling
        """
        label = QLabel(text)
        label.setWordWrap(True)
        label.setStyleSheet("""
            QLabel {
                color: #666;
                font-style: italic;
                padding: 5px;
                margin-bottom: 10px;
            }
        """)
        return label

    def create_section_group(self, title: str) -> QGroupBox:
        """
        Create a standardized section group box.

        Args:
            title: Section title

        Returns:
            Configured QGroupBox for sections
        """
        group = QGroupBox(title)
        group.setStyleSheet("""
            QGroupBox {
                font-weight: bold;
                border: 1px solid #aaaaaa;
                border-radius: 5px;
                margin-top: 1ex;
                padding-top: 5px;
            }
            QGroupBox::title {
                subcontrol-origin: margin;
                left: 10px;
                padding: 0 5px 0 5px;
            }
        """)
        return group

    def create_standard_spinner(self, min_val: int, max_val: int, default_val: int,
                               suffix: str = "", tooltip: str = "") -> QSpinBox:
        """
        Create a standardized spinner widget.

        Args:
            min_val: Minimum value
            max_val: Maximum value
            default_val: Default value
            suffix: Optional suffix text
            tooltip: Optional tooltip text

        Returns:
            Configured QSpinBox
        """
        spinner = QSpinBox()
        spinner.setRange(min_val, max_val)
        spinner.setValue(default_val)
        if suffix:
            spinner.setSuffix(suffix)
        if tooltip:
            spinner.setToolTip(tooltip)
        return spinner

    def create_standard_checkbox(self, text: str, tooltip: str = "",
                                checked: bool = False) -> QCheckBox:
        """
        Create a standardized checkbox widget.

        Args:
            text: Checkbox text
            tooltip: Optional tooltip text
            checked: Initial checked state

        Returns:
            Configured QCheckBox
        """
        checkbox = QCheckBox(text)
        checkbox.setChecked(checked)
        if tooltip:
            checkbox.setToolTip(tooltip)
        return checkbox

    def create_standard_combo(self, items: list, tooltip: str = "",
                             default_index: int = 0) -> QComboBox:
        """
        Create a standardized combo box widget.

        Args:
            items: List of items for the combo box
            tooltip: Optional tooltip text
            default_index: Default selected index

        Returns:
            Configured QComboBox
        """
        combo = QComboBox()
        combo.addItems(items)
        combo.setCurrentIndex(default_index)
        if tooltip:
            combo.setToolTip(tooltip)
        return combo

    def create_standard_button(self, text: str, tooltip: str = "",
                              style: str = "primary") -> QPushButton:
        """
        Create a standardized button widget.

        Args:
            text: Button text
            tooltip: Optional tooltip text
            style: Button style ("primary", "secondary", "danger")

        Returns:
            Configured QPushButton
        """
        button = QPushButton(text)
        if tooltip:
            button.setToolTip(tooltip)

        # Apply standard button styling
        if style == "primary":
            button.setStyleSheet("""
                QPushButton {
                    background-color: #007acc;
                    color: white;
                    border: none;
                    padding: 8px 16px;
                    border-radius: 4px;
                    font-weight: bold;
                }
                QPushButton:hover {
                    background-color: #005a9e;
                }
                QPushButton:pressed {
                    background-color: #004080;
                }
            """)
        elif style == "secondary":
            button.setStyleSheet("""
                QPushButton {
                    background-color: #f0f0f0;
                    color: #333;
                    border: 1px solid #ccc;
                    padding: 8px 16px;
                    border-radius: 4px;
                }
                QPushButton:hover {
                    background-color: #e0e0e0;
                }
                QPushButton:pressed {
                    background-color: #d0d0d0;
                }
            """)
        elif style == "danger":
            button.setStyleSheet("""
                QPushButton {
                    background-color: #dc3545;
                    color: white;
                    border: none;
                    padding: 8px 16px;
                    border-radius: 4px;
                    font-weight: bold;
                }
                QPushButton:hover {
                    background-color: #c82333;
                }
                QPushButton:pressed {
                    background-color: #bd2130;
                }
            """)

        return button

    def create_separator(self) -> QFrame:
        """
        Create a standardized separator line.

        Returns:
            Configured QFrame as separator
        """
        separator = QFrame()
        separator.setFrameShape(QFrame.Shape.HLine)
        separator.setFrameShadow(QFrame.Shadow.Sunken)
        separator.setStyleSheet("QFrame { color: #cccccc; }")
        return separator

    # ========== STANDARDIZED LAYOUT CREATION METHODS ==========

    def create_standard_layout(self, parent_layout, description: str = None) -> QVBoxLayout:
        """
        Create a standardized main layout for the tag configuration.

        Args:
            parent_layout: Parent layout to add to
            description: Optional description text

        Returns:
            Main QVBoxLayout for the configuration
        """
        # Create main group
        main_group = self.create_main_group()
        layout = QVBoxLayout()

        # Add description if provided
        if description:
            desc_label = self.create_description_label(description)
            layout.addWidget(desc_label)

        main_group.setLayout(layout)
        parent_layout.addWidget(main_group)

        return layout

    def create_form_section(self, parent_layout, title: str = None) -> QFormLayout:
        """
        Create a standardized form section.

        Args:
            parent_layout: Parent layout to add to
            title: Optional section title

        Returns:
            QFormLayout for form controls
        """
        if title:
            section_group = self.create_section_group(title)
            form_layout = QFormLayout()
            section_group.setLayout(form_layout)
            parent_layout.addWidget(section_group)
        else:
            form_layout = QFormLayout()
            parent_layout.addLayout(form_layout)

        return form_layout

    def create_horizontal_section(self, parent_layout, title: str = None) -> QHBoxLayout:
        """
        Create a standardized horizontal section.

        Args:
            parent_layout: Parent layout to add to
            title: Optional section title

        Returns:
            QHBoxLayout for horizontal controls
        """
        if title:
            section_group = self.create_section_group(title)
            h_layout = QHBoxLayout()
            section_group.setLayout(h_layout)
            parent_layout.addWidget(section_group)
        else:
            h_layout = QHBoxLayout()
            parent_layout.addLayout(h_layout)

        return h_layout

    def create_vertical_section(self, parent_layout, title: str = None) -> QVBoxLayout:
        """
        Create a standardized vertical section.

        Args:
            parent_layout: Parent layout to add to
            title: Optional section title

        Returns:
            QVBoxLayout for vertical controls
        """
        if title:
            section_group = self.create_section_group(title)
            v_layout = QVBoxLayout()
            section_group.setLayout(v_layout)
            parent_layout.addWidget(section_group)
        else:
            v_layout = QVBoxLayout()
            parent_layout.addLayout(v_layout)

        return v_layout

    # ========== STANDARDIZED WIDGET MANAGEMENT ==========

    def add_widget_with_signals(self, widget_name: str, widget: QWidget,
                               auto_connect: bool = True) -> None:
        """
        Store a widget and optionally connect its change signals.

        Args:
            widget_name: Name to store the widget under
            widget: The widget to store
            auto_connect: Whether to automatically connect change signals
        """
        self.store_widget(widget_name, widget)
        if auto_connect:
            self.connect_change_signals(widget)

    def create_inline_piece_selector(self, label: str, allow_costs: bool = True) -> 'InlinePieceSelector':
        """
        Create a standardized inline piece selector.

        Args:
            label: Label for the selector
            allow_costs: Whether to allow cost configuration

        Returns:
            Configured InlinePieceSelector
        """
        try:
            from dialogs.inline_selectors import InlinePieceSelector
            return InlinePieceSelector(self.editor, label, allow_costs=allow_costs)
        except ImportError:
            self.log_error("InlinePieceSelector not available")
            return None

    def create_range_editor_button(self, button_text: str = "Edit Pattern...",
                                  callback_method: str = None) -> QPushButton:
        """
        Create a standardized range editor button.

        Args:
            button_text: Text for the button
            callback_method: Name of the method to call when clicked

        Returns:
            Configured QPushButton
        """
        button = self.create_standard_button(button_text, "Open range editor to configure pattern")

        if callback_method and hasattr(self, callback_method):
            button.clicked.connect(getattr(self, callback_method))

        return button
