"""
SwapPlaces tag configuration for ability editor.
Handles swap places ability configurations with target selection and restrictions.
"""

from PyQt6.QtWidgets import (QFormLayout, QComboBox, QCheckBox, QSpinBox,
                            QWidget, QLabel, QPushButton, QHBoxLayout)
from typing import Dict, Any
from .base_tag_config import BaseTagConfig
from core.ui import InlinePieceSelector
from dialogs.range_editor_dialog import edit_target_range


class SwapPlacesConfig(BaseTagConfig):
    """Configuration for swapPlaces tag abilities."""

    def __init__(self, editor):
        super().__init__(editor, "swapPlaces")
        # Initialize swap range data
        self.swap_range_pattern = [[False] * 8 for _ in range(8)]
        self.swap_piece_position = [3, 3]
        self.swap_checkbox_states = {}
    
    def create_ui(self, parent_layout) -> None:
        """
        Create the UI widgets for swap places configuration.
        
        Args:
            parent_layout: The layout to add widgets to
        """
        try:
            self.log_debug("Starting swap places UI creation")
            
            # Create main widget
            main_widget = QWidget()
            form_layout = QFormLayout()
            
            # Target pieces selector
            self.swap_piece_selector = InlinePieceSelector(
                parent=self.editor,
                title="Swap Targets",
                allow_costs=False
            )
            form_layout.addRow("Target Pieces:", self.swap_piece_selector)
            self.store_widget("swap_targets", self.swap_piece_selector)

            # Swap range editor
            swap_range_layout = QHBoxLayout()
            swap_range_button = QPushButton("Edit Swap Range")
            swap_range_button.setToolTip("Configure which squares can be swapped to")
            swap_range_button.clicked.connect(self.edit_swap_range)
            self.store_widget("swap_range_button", swap_range_button)
            swap_range_layout.addWidget(swap_range_button)
            form_layout.addRow("Swap Range:", swap_range_layout)

            # Stun effect spinner
            stun_spin = QSpinBox()
            stun_spin.setRange(0, 10)
            stun_spin.setValue(0)
            stun_spin.setSpecialValueText("No stun")
            stun_spin.setSuffix(" turns")
            stun_spin.setToolTip("Number of turns swapped pieces are stunned (0 = no stun)")
            self.store_widget("swap_stun_turns", stun_spin)
            form_layout.addRow("Stun Duration:", stun_spin)
            
            main_widget.setLayout(form_layout)
            parent_layout.addWidget(main_widget)
            
            # Connect change signals for all widgets
            self.connect_change_signals(self.swap_piece_selector)
            self.connect_change_signals(stun_spin)
            
            self.log_debug("Swap places UI creation completed")

        except Exception as e:
            self.log_error(f"Error creating UI: {e}")

    def edit_swap_range(self):
        """Open the swap range editor dialog."""
        try:
            result = edit_target_range(
                initial_pattern=self.swap_range_pattern,
                piece_position=self.swap_piece_position,
                title="Edit Swap Range",
                parent=self.editor,
                checkbox_states=self.swap_checkbox_states
            )

            if result[0] is not None:  # User didn't cancel
                self.swap_range_pattern, self.swap_piece_position, self.swap_checkbox_states = result
                self.log_debug("Swap range updated")

        except Exception as e:
            self.log_error(f"Error editing swap range: {e}")
    
    def populate_data(self, data: Dict[str, Any]) -> None:
        """
        Populate the UI widgets with data from an ability.
        
        Args:
            data: Dictionary containing ability data
        """
        try:
            self.log_debug(f"Starting swap places data population with: {data}")
            
            # Target pieces
            piece_selector = self.get_widget_by_name("swap_targets")
            if piece_selector and "swapTargets" in data:
                piece_selector.set_pieces(data["swapTargets"])

            # Swap range data
            if "swapRangePattern" in data:
                self.swap_range_pattern = data["swapRangePattern"]
            if "swapRangePosition" in data:
                self.swap_piece_position = data["swapRangePosition"]
            if "swapRangeCheckboxes" in data:
                self.swap_checkbox_states = data["swapRangeCheckboxes"]

            # Stun duration
            stun_spin = self.get_widget_by_name("swap_stun_turns")
            if stun_spin and "swapStunTurns" in data:
                stun_spin.setValue(data["swapStunTurns"])
            

            
            self.log_debug("Swap places data populated successfully")
            
        except Exception as e:
            self.log_error(f"Error populating data: {e}")
    
    def collect_data(self) -> Dict[str, Any]:
        """
        Collect data from the UI widgets.
        
        Returns:
            Dictionary containing the swap places configuration data
        """
        try:
            data = {}
            
            # Target pieces
            piece_selector = self.get_widget_by_name("swap_targets")
            if piece_selector:
                data["swapTargets"] = piece_selector.get_pieces()

            # Swap range data
            data["swapRangePattern"] = self.swap_range_pattern
            data["swapRangePosition"] = self.swap_piece_position
            data["swapRangeCheckboxes"] = self.swap_checkbox_states

            # Stun duration
            stun_spin = self.get_widget_by_name("swap_stun_turns")
            if stun_spin:
                data["swapStunTurns"] = stun_spin.value()
            
            self.log_debug(f"Collected swap places data: {data}")
            return data
            
        except Exception as e:
            self.log_error(f"Error collecting data: {e}")
            return {}
