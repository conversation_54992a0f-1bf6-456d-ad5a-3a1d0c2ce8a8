"""
Consolidated Inline Selection Widgets for Adventure Chess Creator

This module consolidates the inline selection functionality from core/ui/inline_selection_widgets.py
into a more maintainable structure using the base dialog patterns and eliminating redundancy.

Moved to dialogs folder as these widgets are dialog-like components that fit better here.
"""

import logging
from abc import abstractmethod
from typing import Dict, Any, List, Optional
from PyQt6.QtCore import Qt, pyqtSignal
from PyQt6.QtGui import QFont
from PyQt6.QtWidgets import (
    QWidget, QVBoxLayout, QHBoxLayout, QFormLayout, QFrame,
    QLabel, QPushButton, QScrollArea, QSpinBox, QComboBox
)

# Import base dialog functionality for consistent styling
from .base_dialog import BaseDialog
from core.ui import (
    apply_theme_to_widget,
    create_themed_label,
    create_themed_button,
    create_themed_group_box
)

logger = logging.getLogger(__name__)


class BaseInlineSelector(QWidget):
    """
    Base class for inline selection widgets.
    
    Provides common functionality for piece and ability selectors,
    eliminating code duplication and using consistent theming.
    """
    
    items_changed = pyqtSignal()  # Emitted when items list changes
    
    def __init__(self, parent=None, title: str = "Items", allow_costs: bool = True):
        """
        Initialize the base inline selector.
        
        Args:
            parent: Parent widget
            title: Title for the selector
            allow_costs: Whether to show cost controls
        """
        super().__init__(parent)
        self.title = title
        self.allow_costs = allow_costs
        self.items = []  # List of item dictionaries
        self.selected_index = -1
        
        self.setup_ui()
        self.apply_theme()
    
    def setup_ui(self):
        """Setup the base UI structure."""
        layout = QVBoxLayout(self)
        layout.setContentsMargins(0, 0, 0, 0)
        layout.setSpacing(8)
        
        # Title
        title_label = create_themed_label(f"{self.title}:", 'header_label')
        layout.addWidget(title_label)
        
        # Main content area with scroll
        self.scroll_area = QScrollArea()
        self.scroll_area.setWidgetResizable(True)
        self.scroll_area.setHorizontalScrollBarPolicy(Qt.ScrollBarPolicy.ScrollBarAlwaysOff)
        self.scroll_area.setVerticalScrollBarPolicy(Qt.ScrollBarPolicy.ScrollBarAsNeeded)
        
        # Content widget
        self.content_widget = QWidget()
        self.content_layout = QVBoxLayout(self.content_widget)
        self.content_layout.setContentsMargins(5, 5, 5, 5)
        self.content_layout.setSpacing(5)
        
        self.scroll_area.setWidget(self.content_widget)
        layout.addWidget(self.scroll_area)
        
        # Add button
        self.add_button = create_themed_button(f"Add {self.title[:-1]}", 'primary_button')
        self.add_button.clicked.connect(self.add_item)
        layout.addWidget(self.add_button)
        
        # Setup specific UI elements
        self.setup_specific_ui()
    
    @abstractmethod
    def setup_specific_ui(self):
        """Setup selector-specific UI elements. Override in subclasses."""
        pass
    
    @abstractmethod
    def create_item_widget(self, item_data: Dict[str, Any]) -> QWidget:
        """Create a widget for displaying a single item. Override in subclasses."""
        pass
    
    @abstractmethod
    def add_item(self):
        """Add a new item. Override in subclasses."""
        pass
    
    def create_item_frame(self) -> QFrame:
        """Create a standardized item frame with consistent theming."""
        frame = QFrame()
        frame.setFrameStyle(QFrame.Shape.Box)
        frame.setLineWidth(1)
        apply_theme_to_widget(frame, 'item_frame')
        return frame
    
    def create_remove_button(self, index: int) -> QPushButton:
        """Create a standardized remove button."""
        button = create_themed_button("Remove", 'danger_button')
        button.clicked.connect(lambda: self.remove_item(index))
        return button
    
    def remove_item(self, index: int):
        """Remove an item by index."""
        if 0 <= index < len(self.items):
            self.items.pop(index)
            self.refresh_display()
            self.items_changed.emit()
    
    def refresh_display(self):
        """Refresh the display of all items."""
        # Clear existing items
        for i in reversed(range(self.content_layout.count())):
            child = self.content_layout.itemAt(i).widget()
            if child:
                child.setParent(None)
        
        # Add all items
        for i, item_data in enumerate(self.items):
            item_widget = self.create_item_widget(item_data)
            self.content_layout.addWidget(item_widget)
        
        # Add stretch to push items to top
        self.content_layout.addStretch()
    
    def apply_theme(self):
        """Apply consistent theming to the selector."""
        apply_theme_to_widget(self, 'selector_widget')
        apply_theme_to_widget(self.scroll_area, 'scroll_area')
    
    def get_items(self) -> List[Dict[str, Any]]:
        """Get all items."""
        return self.items.copy()
    
    def set_items(self, items: List[Dict[str, Any]]):
        """Set all items."""
        self.items = items.copy()
        self.refresh_display()
        self.items_changed.emit()
    
    def clear_items(self):
        """Clear all items."""
        self.items.clear()
        self.refresh_display()
        self.items_changed.emit()
    
    def count(self) -> int:
        """Get number of items."""
        return len(self.items)


class InlinePieceSelector(BaseInlineSelector):
    """
    Inline piece selector widget using the consolidated base class.
    
    Displays selected pieces in a scrollable list with add/remove functionality.
    """
    
    pieces_changed = pyqtSignal()  # Alias for items_changed for backward compatibility
    
    def __init__(self, parent=None, title: str = "Pieces", allow_costs: bool = True):
        super().__init__(parent, title, allow_costs)
        # Connect base signal to specific signal for backward compatibility
        self.items_changed.connect(self.pieces_changed.emit)
    
    def setup_specific_ui(self):
        """Setup piece-specific UI elements."""
        # Add any piece-specific controls here if needed
        pass
    
    def create_item_widget(self, piece_data: Dict[str, Any]) -> QWidget:
        """Create a widget for displaying a single piece."""
        frame = self.create_item_frame()
        layout = QHBoxLayout(frame)
        layout.setContentsMargins(8, 6, 8, 6)
        layout.setSpacing(8)
        
        # Piece info
        info_text = f"Target: {piece_data.get('target', 'Any')}, Piece: {piece_data.get('piece', 'Unknown')}"
        if self.allow_costs and 'cost' in piece_data:
            info_text += f", Cost: {piece_data['cost']}"
        
        info_label = create_themed_label(info_text, 'item_label')
        layout.addWidget(info_label)
        
        layout.addStretch()
        
        # Remove button
        remove_btn = self.create_remove_button(len(self.items) - 1)
        layout.addWidget(remove_btn)
        
        return frame
    
    def add_item(self):
        """Add a new piece item."""
        # For now, add a default piece - this could be enhanced with a dialog
        new_piece = {
            'target': 'Any',
            'piece': 'New Piece',
            'cost': 1 if self.allow_costs else 0
        }
        self.items.append(new_piece)
        self.refresh_display()
        self.items_changed.emit()
    
    # Backward compatibility methods
    @property
    def pieces(self):
        """Backward compatibility property."""
        return self.items
    
    @pieces.setter
    def pieces(self, value):
        """Backward compatibility property setter."""
        self.set_items(value)


class InlineAbilitySelector(BaseInlineSelector):
    """
    Inline ability selector widget using the consolidated base class.
    
    Displays selected abilities in a scrollable list with add/remove functionality.
    """
    
    abilities_changed = pyqtSignal()  # Alias for items_changed for backward compatibility
    
    def __init__(self, parent=None, title: str = "Abilities", allow_costs: bool = True):
        super().__init__(parent, title, allow_costs)
        # Connect base signal to specific signal for backward compatibility
        self.items_changed.connect(self.abilities_changed.emit)
    
    def setup_specific_ui(self):
        """Setup ability-specific UI elements."""
        # Add any ability-specific controls here if needed
        pass
    
    def create_item_widget(self, ability_data: Dict[str, Any]) -> QWidget:
        """Create a widget for displaying a single ability."""
        frame = self.create_item_frame()
        layout = QHBoxLayout(frame)
        layout.setContentsMargins(8, 6, 8, 6)
        layout.setSpacing(8)
        
        # Ability info
        info_text = f"Ability: {ability_data.get('ability', 'Unknown')}"
        if self.allow_costs and 'cost' in ability_data:
            info_text += f", Cost: {ability_data['cost']}"
        
        info_label = create_themed_label(info_text, 'item_label')
        layout.addWidget(info_label)
        
        layout.addStretch()
        
        # Remove button
        remove_btn = self.create_remove_button(len(self.items) - 1)
        layout.addWidget(remove_btn)
        
        return frame
    
    def add_item(self):
        """Add a new ability item."""
        # For now, add a default ability - this could be enhanced with a dialog
        new_ability = {
            'ability': 'New Ability',
            'cost': 1 if self.allow_costs else 0
        }
        self.items.append(new_ability)
        self.refresh_display()
        self.items_changed.emit()
    
    # Backward compatibility methods
    @property
    def abilities(self):
        """Backward compatibility property."""
        return self.items
    
    @abilities.setter
    def abilities(self, value):
        """Backward compatibility property setter."""
        self.set_items(value)
