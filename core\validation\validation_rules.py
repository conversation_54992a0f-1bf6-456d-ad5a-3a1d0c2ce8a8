"""
Enhanced Validation Rules for Adventure Chess Creator

This module provides comprehensive validation rules for data integrity,
security, and consistency across the application.
"""

import re
from typing import Any, List, Union

# Import centralized security patterns
# Security patterns removed - using standard Python validation instead


class ValidationRules:
    """
    Enhanced validation rules for Adventure Chess Creator models
    """

    # Standard validation patterns (replaced security over-engineering)
    DANGEROUS_PATTERNS = [
        r'<script[^>]*>.*?</script>',  # Basic XSS protection
        r'javascript:',                # JavaScript URLs
        r'on\w+\s*=',                 # Event handlers
    ]
    MAX_LENGTHS = {
        "name": 100,
        "description": 1000,
        "tag_name": 50,
        "tag_value": 200
    }

    # Valid ranges for numeric fields
    NUMERIC_RANGES = {
        "max_points": (0, 999),
        "starting_points": (0, 999),
        "distance": (1, 8),
        "duration": (1, 100),
        "coordinate": (0, 7),
        "pattern_value": (0, 1),
    }

    @classmethod
    def validate_string_field(
        cls, value: str, field_name: str, allow_empty: bool = False
    ) -> str:
        """
        Enhanced string validation with security checks

        Args:
            value: String value to validate
            field_name: Name of the field being validated
            allow_empty: Whether empty strings are allowed

        Returns:
            Validated and sanitized string

        Raises:
            ValueError: If validation fails
        """
        if value is None:
            if allow_empty:
                return ""
            else:
                raise ValueError(f"{field_name} cannot be None")

        # Convert to string if not already
        if not isinstance(value, str):
            value = str(value)

        # Check for empty string
        if not value.strip() and not allow_empty:
            raise ValueError(f"{field_name} cannot be empty")

        # Check length
        max_length = cls.MAX_LENGTHS.get(field_name, 500)
        if len(value) > max_length:
            raise ValueError(
                f"{field_name} exceeds maximum length of {max_length} characters"
            )

        # Security validation - check for dangerous patterns
        for pattern in cls.DANGEROUS_PATTERNS:
            if re.search(pattern, value, re.IGNORECASE):
                raise ValueError(f"{field_name} contains potentially dangerous content")

        # Basic sanitization
        value = value.strip()

        return value

    @classmethod
    def validate_numeric_field(
        cls, value: Union[int, float], field_name: str
    ) -> Union[int, float]:
        """
        Enhanced numeric validation with range checks

        Args:
            value: Numeric value to validate
            field_name: Name of the field being validated

        Returns:
            Validated numeric value

        Raises:
            ValueError: If validation fails
        """
        if value is None:
            raise ValueError(f"{field_name} cannot be None")

        # Convert to appropriate numeric type
        if isinstance(value, str):
            try:
                if "." in value:
                    value = float(value)
                else:
                    value = int(value)
            except ValueError:
                raise ValueError(f"{field_name} must be a valid number")

        if not isinstance(value, (int, float)):
            raise ValueError(f"{field_name} must be a number")

        # Check for valid range
        if field_name in cls.NUMERIC_RANGES:
            min_val, max_val = cls.NUMERIC_RANGES[field_name]
            if value < min_val or value > max_val:
                raise ValueError(
                    f"{field_name} must be between {min_val} and {max_val}"
                )

        # Check for reasonable bounds (prevent extremely large numbers)
        if abs(value) > 1000000:
            raise ValueError(f"{field_name} value is unreasonably large")

        return value

    @classmethod
    def validate_list_field(
        cls,
        value: List[Any],
        field_name: str,
        max_items: int = 100,
        allow_empty: bool = True,
    ) -> List[Any]:
        """
        Enhanced list validation

        Args:
            value: List value to validate
            field_name: Name of the field being validated
            max_items: Maximum number of items allowed
            allow_empty: Whether empty lists are allowed

        Returns:
            Validated list

        Raises:
            ValueError: If validation fails
        """
        if value is None:
            if allow_empty:
                return []
            else:
                raise ValueError(f"{field_name} cannot be None")

        if not isinstance(value, list):
            raise ValueError(f"{field_name} must be a list")

        if not value and not allow_empty:
            raise ValueError(f"{field_name} cannot be empty")

        if len(value) > max_items:
            raise ValueError(f"{field_name} cannot have more than {max_items} items")

        return value

    @classmethod
    def validate_coordinate(
        cls, value: Union[int, List[int]], field_name: str
    ) -> Union[int, List[int]]:
        """
        Validate coordinate values for chess board positions

        Args:
            value: Coordinate value (single int or list of ints)
            field_name: Name of the field being validated

        Returns:
            Validated coordinate

        Raises:
            ValueError: If validation fails
        """
        if isinstance(value, list):
            for i, coord in enumerate(value):
                if not isinstance(coord, int) or coord < 0 or coord > 7:
                    raise ValueError(
                        f"{field_name}[{i}] must be an integer between 0 and 7"
                    )
            return value
        elif isinstance(value, int):
            if value < 0 or value > 7:
                raise ValueError(f"{field_name} must be an integer between 0 and 7")
            return value
        else:
            raise ValueError(f"{field_name} must be an integer or list of integers")

    @classmethod
    def validate_pattern_grid(
        cls, value: List[List[int]], field_name: str = "pattern"
    ) -> List[List[int]]:
        """
        Validate movement pattern grid

        Args:
            value: 2D grid representing movement pattern
            field_name: Name of the field being validated

        Returns:
            Validated pattern grid

        Raises:
            ValueError: If validation fails
        """
        if not isinstance(value, list):
            raise ValueError(f"{field_name} must be a list")

        if len(value) != 8:
            raise ValueError(f"{field_name} must have exactly 8 rows")

        for i, row in enumerate(value):
            if not isinstance(row, list):
                raise ValueError(f"{field_name} row {i} must be a list")

            if len(row) != 8:
                raise ValueError(f"{field_name} row {i} must have exactly 8 columns")

            for j, cell in enumerate(row):
                if not isinstance(cell, int) or cell < 0 or cell > 3:
                    raise ValueError(
                        f"{field_name}[{i}][{j}] must be an integer between 0 and 3"
                    )

        return value


class EnhancedValidationMixin:
    """
    Mixin class to add enhanced validation to Pydantic models
    """

    @classmethod
    def validate_name(cls, v):
        return ValidationRules.validate_string_field(v, "name")

    @classmethod
    def validate_description(cls, v):
        return ValidationRules.validate_string_field(v, "description", allow_empty=True)

    @classmethod
    def validate_version(cls, v):
        validated = ValidationRules.validate_string_field(v, "version")
        # Additional version format validation
        if not re.match(r"^\d+\.\d+\.\d+$", validated):
            raise ValueError("version must be in format X.Y.Z (e.g., 1.0.0)")
        return validated


def create_piece_validation_rules():
    """Create validation rules specific to piece data"""

    def validate_piece_name(data):
        name = data.get("name", "")
        if not name or not name.strip():
            return False, "Piece name is required"
        if len(name) > 50:
            return False, "Piece name must be 50 characters or less"
        return True, "Piece name is valid"

    def validate_piece_value(data):
        value = data.get("value", 0)
        if not isinstance(value, (int, float)) or value < 0:
            return False, "Piece value must be a non-negative number"
        if value > 100:
            return False, "Piece value seems unusually high (max recommended: 100)"
        return True, "Piece value is valid"

    def validate_piece_movement(data):
        movement = data.get("movement", {})
        if not isinstance(movement, dict):
            return False, "Movement data must be a dictionary"

        # Check for required movement fields
        required_fields = ["pattern"]
        for field in required_fields:
            if field not in movement:
                return False, f"Movement is missing required field: {field}"

        return True, "Piece movement is valid"

    return [
        ("Piece Name", validate_piece_name),
        ("Piece Value", validate_piece_value),
        ("Piece Movement", validate_piece_movement),
    ]


def create_ability_validation_rules():
    """Create validation rules specific to ability data"""

    def validate_ability_name(data):
        name = data.get("name", "")
        if not name or not name.strip():
            return False, "Ability name is required"
        if len(name) > 100:
            return False, "Ability name must be 100 characters or less"
        return True, "Ability name is valid"

    def validate_ability_cost(data):
        cost = data.get("cost", 0)
        if not isinstance(cost, (int, float)) or cost < 0:
            return False, "Ability cost must be a non-negative number"
        if cost > 50:
            return False, "Ability cost seems unusually high (max recommended: 50)"
        return True, "Ability cost is valid"

    def validate_ability_tags(data):
        tags = data.get("tags", [])
        if not isinstance(tags, list):
            return False, "Ability tags must be a list"
        if not tags:
            return False, "At least one ability tag is required"
        return True, "Ability tags are valid"

    return [
        ("Ability Name", validate_ability_name),
        ("Ability Cost", validate_ability_cost),
        ("Ability Tags", validate_ability_tags),
    ]
