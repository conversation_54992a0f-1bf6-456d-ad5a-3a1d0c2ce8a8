"""
Dialog modules for Adventure Chess

Location: dialogs/

Contains all dialog windows and popup interfaces:
- base_dialog.py: Base classes for consistent dialog behavior
- inline_selectors.py: Consolidated inline selection widgets (moved from core/ui)
- pattern_editor_dialog.py: Movement pattern editing
- piece_ability_manager.py: Piece-ability relationship management
- range_editor_dialog.py: Range and spatial configuration
- unified_adjacency_dialog.py: Adjacency requirement settings

Used by: Editors for complex configuration tasks requiring dedicated UI
"""

# Import consolidated inline selectors for easy access
from .inline_selectors import (
    BaseInlineSelector,
    InlinePieceSelector,
    InlineAbilitySelector
)

__all__ = [
    'BaseInlineSelector',
    'InlinePieceSelector',
    'InlineAbilitySelector'
]
