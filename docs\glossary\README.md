# Adventure Chess Glossary Documentation

This folder contains versioned documentation for the Adventure Chess piece and ability editors.

**📚 HISTORICAL RECORD POLICY**: All glossary versions are intentionally preserved to maintain a complete development history. Unlike other parts of the codebase that are being cleaned up and consolidated, the glossary serves as a historical archive of the project's evolution and should NOT be archived or removed.

## Version Overview

### v1.1.0 (Current) - Architecture Modernization Complete
- **Core Module System**: Complete restructuring into 10 specialized subsystems with base classes, handlers, and interfaces
- **Component-Based Editors**: Refactored piece and ability editors with separated data handlers, UI components, and managers
- **Enhanced Schema System**: Flexible Pydantic validation with legacy compatibility and permissive validation modes
- **Professional UI Framework**: Dark theme integration, responsive design, and enhanced preview components
- **Performance Optimization**: Lazy loading systems, caching frameworks, and file system optimization
- **Security Framework**: Comprehensive data validation, error boundaries, and crash recovery systems
- **Advanced Testing**: Comprehensive test suite with unit, integration, UI, performance, and stress testing
- **Complete Documentation**: Developer onboarding guides, architectural documentation, and troubleshooting resources

### v1.0.9 - Codebase Modernization Complete
- **Critical Bug Fixes**: Infinite recursion prevention in quick load dropdowns with PyQt6 signal blocking patterns
- **Architecture Modernization**: Complete enhancement folder elimination and modular system consolidation
- **Code Quality Enhancements**: Debug code cleanup, cache artifact removal, and comprehensive documentation
- **Data Format Improvements**: Readable movement patterns and UI component refinements
- **Documentation Enhancement**: Core directory README and comprehensive folder path guidance
- **Comprehensive Testing**: Single source of truth verification and stress testing validation

### v1.0.8 - Editor Refactoring Complete
- **Preview Tab System**: Real-time JSON preview in both editors with refresh functionality
- **Enhanced Tab Layout**: Piece Editor converted to tab system for better organization
- **Configuration Refinements**: Improved UI patterns for Buff, Area Effect, Revival, and Share Space configurations
- **Base Chess Content**: Complete standard chess pieces and abilities using the modular tag system
- **Developer Tools**: JSON preview with syntax highlighting for debugging and development
- **Integration Testing**: Comprehensive validation and testing completed

### v1.0.7 - Production-Ready Documentation
- **Complete Architecture Coverage**: Every component documented with enhanced performance features
- **Enhanced Performance**: Lazy loading, caching, and security improvements implemented
- **Codebase Optimization**: Legacy cleanup, test suite enhancement, and future-ready architecture
- **Validated Systems**: 129 passing tests with comprehensive validation and quality assurance

### v1.0.6 - Comprehensive Application Documentation
- **Complete Architecture Coverage**: Every component, class, and concept documented with accessible explanations
- **Comprehensive Data Flow Documentation**: Detailed step-by-step data flow documentation implemented within the glossary
- **Production Analysis**: Critical next steps and improvement opportunities identified for production deployment
- **Accessible Language**: All technical terms explained in layman's terms for broader understanding
- **Complete Reference**: 28 canonical abilities, configuration options, dialog systems, and troubleshooting guides
- **Best Practices**: Guidelines for effective piece and ability creation with quality assurance procedures

### v1.0.5 - Pydantic Integration Complete
- **Streamlined Architecture**: Unified data flow through Pydantic bridge
- **Code Cleanup**: All deprecated managers removed, duplicate code eliminated
- **100% Field Coverage**: All UI fields mapped to Pydantic schemas
- **Dialog Integration**: Range, pattern, and adjacency editors fully integrated
- **Migration System**: Complete backward compatibility with automatic upgrades
- **Production Ready**: Full validation, error handling, and data integrity

### v1.0.4 - File Structure Reorganization & Comprehensive Updates
- **File Structure Reorganization**: Logical folder structure with dialogs/, editors/, managers/, core/, ui/, utils/
- **Range Editor Integration**: Custom range patterns for carryPiece, passThrough, and other abilities
- **Dialog Integration**: Piece selector and ability selector dialogs fully integrated
- **Revival Configuration Enhanced**: Proper indentation and max pieces per turn spinner
- **Uncheck Functionality**: Ability to uncheck abilities from configuration tab
- **Comprehensive Documentation**: All potential updates integrated and implementation status tracked

### v1.0.3 - Canonical Abilities & Production Ready
- **Canonical Compliance**: All 28 canonical abilities documented and verified
- **Backend Cleanup**: Removed deprecated abilities (preventAbility, revealTiles)
- **Centralized Configuration**: Updated cost calculation and validation systems
- **Pattern Editor Improvements**: Enhanced legend clicking and layout consolidation
- **Production Ready**: Clean foundation for game logic implementation

### v1.0.2 - Comprehensive Reference
- **Multiple Indexes**: Quick reference, backend developer, tooltip reference
- **Complete Coverage**: All ability tags and configuration options documented
- **Technical Details**: Data structures, validation rules, best practices
- **Troubleshooting**: Common issues and solutions
- **Backend Focus**: Detailed information for developers implementing game logic

### v1.0.1 - Enhanced Editors
- **Documented Improvements**: Range and pattern editor enhancements
- **Quick Patterns**: Chess piece buttons for instant range setting
- **5-Color System**: Pattern editor with action and any types
- **UI Improvements**: Selectable patterns, better synchronization

### v1.0.0 - Initial Release
- **Basic Documentation**: Core piece and ability editor functionality
- **Tag System**: Initial ability tag descriptions
- **File Management**: Save/load system documentation

## Usage Guidelines

### For End Users
- **Start with**: Quick Reference Index in v1.1.0 for immediate access to key concepts and latest architectural improvements
- **Stability**: Benefit from critical bug fixes and infinite recursion prevention
- **Data Formats**: Use new readable movement pattern storage for easier visualization
- **Enhanced UI**: Experience refined interface with improved component behavior
- **Troubleshooting**: Complete troubleshooting guide with enhanced error resolution

### For Developers
- **Primary Resource**: v1.1.0 comprehensive documentation with complete architectural modernization coverage
- **Architecture**: Understand modernized modular structure with enhancement folder elimination
- **Code Quality**: Learn from comprehensive cleanup and documentation enhancement patterns
- **Core Directory**: Use new README for developer onboarding and upgrade guidance
- **Signal Management**: Implement PyQt6 signal blocking patterns for stable UI operations
- **Testing**: Comprehensive validation and stress testing methodologies

### For Documentation Updates
- **Version Naming**: Use semantic versioning (major.minor.patch)
- **Change Documentation**: Always document improvements in new versions
- **Backward Compatibility**: Maintain links to previous versions
- **Index Updates**: Keep all indexes current with actual editor capabilities

### Creating New Glossary Versions - AI/User Guide

#### **Pre-Creation Checklist**
1. **Read Current Version**: Thoroughly review the latest glossary for structure and content
2. **Identify Changes**: Note any "!!" comments indicating needed updates or refactors
3. **Test Application**: Verify all features work as documented
4. **Check Canonical Compliance**: Ensure all abilities match the canonical ABILITY_TAGS in config.py

#### **Content Requirements for New Versions**
1. **Multiple Indexes**: Always include Quick Reference, Backend Developer, and Tooltip Reference indexes
2. **Canonical Abilities**: Document all 28 canonical abilities with Function, Data, Options, Behavior, and Interactions
3. **UI Function Documentation**: In Data field, specify UI function used (range_editor, pattern_editor, custom_widget, spinner, dropdown, checkbox)
4. **Configuration Hierarchy**: Use indentation to show options revealed by parent fields
5. **OR Mechanics**: Mark mutually exclusive options with * prefix
6. **Configuration Accuracy**: Verify all configuration options match actual UI elements
7. **Remove Deprecated**: Clean out any "!!" marked items that have been fixed
8. **Add New Features**: Document any new functionality or improvements
9. **Potential Updates**: Include comprehensive UI and Backend perspective updates section

#### **Structure Guidelines**
- **Table of Contents**: Always include comprehensive navigation
- **Consistent Formatting**: Use same heading levels and markdown structure
- **Code Examples**: Include JSON structure examples for data formats
- **Visual Clarity**: Use emojis and formatting for easy scanning
- **Cross-References**: Link related sections together

#### **Documentation Format Standards**
- **UI Function Format**: `fieldName` (ui_function: options) - e.g., `areaShape` (dropdown: Circle/Square/Cross/Line/Custom)
- **Indentation Rules**: Use 2-space indentation for revealed/dependent options
- **OR Mechanics**: Use * prefix for mutually exclusive options - e.g., *`delayTurns` OR *`delayActions`
- **Conditional Options**: Show conditions in parentheses - e.g., `customPattern` (when areaShape=Custom)
- **UI Function Types**:
  - `range_editor`: 8x8 grid for range patterns
  - `pattern_editor`: 8x8 grid for movement/action patterns
  - `custom_widget`: Complex UI components (piece lists, etc.)
  - `spinner`: Numeric input with min/max values
  - `dropdown`: Selection from predefined options
  - `checkbox`: Boolean true/false options

#### **Quality Assurance**
- **No Duplicates**: Ensure no duplicate information between sections
- **Accurate Data**: All configuration options must match actual application behavior
- **UI Function Accuracy**: Verify all UI function types match actual implementation
- **Indentation Consistency**: Check that revealed options are properly indented
- **OR Mechanics Verification**: Confirm mutually exclusive options are marked with *
- **Complete Coverage**: Every UI element should have documentation
- **Potential Updates**: Include both UI and Backend perspective future enhancements
- **Version History**: Always include what changed from previous version

## File Structure

```
glossary/
├── README.md                           # This file
├── Adventure_Chess_Glossary_v1-0-0.md  # Initial release
├── Adventure_Chess_Glossary_v1-0-1.md  # Enhanced editors
├── Adventure_Chess_Glossary_v1-0-2.md  # Comprehensive reference
├── Adventure_Chess_Glossary_v1-0-3.md  # Canonical abilities & production ready
├── Adventure_Chess_Glossary_v1-0-4.md  # File structure reorganization & comprehensive updates
├── Adventure_Chess_Glossary_v1-0-5.md  # Pydantic integration complete
├── Adventure_Chess_Glossary_v1-0-6.md  # Comprehensive application documentation
├── Adventure_Chess_Glossary_v1-0-7.md  # Production-ready documentation
├── Adventure_Chess_Glossary_v1-0-8.md  # Editor refactoring complete
├── Adventure_Chess_Glossary_v1-0-9.md  # Codebase modernization complete
└── Adventure_Chess_Glossary_v1-1-0.md  # Architecture modernization complete (current)
```

## Data Flow Documentation

**Important**: Complete data flow documentation is implemented within the Adventure Chess Glossary v1.0.6. This includes:

- **Step-by-step data flow**: From user input through UI widgets to data storage
- **Component interactions**: How different parts of the application communicate
- **Data validation**: Pydantic schema validation and error handling
- **File operations**: Save/load processes with migration and backup systems
- **Bridge patterns**: Translation between UI and data storage layers

The data flow documentation provides both high-level architecture overview and detailed technical implementation, making it accessible to both end users and developers.

## Contributing

When updating documentation:

1. **Create New Version**: Don't overwrite existing versions
2. **Document Changes**: Include version history section
3. **Update Indexes**: Ensure all indexes reflect current capabilities
4. **Validate Content**: Verify all information matches actual editor behavior
5. **Cross-Reference**: Link related sections for easy navigation

## Quick Links

- [Latest Version (v1.1.0)](Adventure_Chess_Glossary_v1-1-0.md) - **Architecture Modernization Complete**
- [Core Module System](Adventure_Chess_Glossary_v1-1-0.md#core-module-system) - **10 specialized subsystems with base classes**
- [Component-Based Editors](Adventure_Chess_Glossary_v1-1-0.md#editor-components) - **Refactored editors with separated concerns**
- [Enhanced Schema System](Adventure_Chess_Glossary_v1-1-0.md#data-management) - **Flexible Pydantic validation**
- [Professional UI Framework](Adventure_Chess_Glossary_v1-1-0.md#ui-framework) - **Dark theme and responsive design**
- [Architecture Overview](Adventure_Chess_Glossary_v1-1-0.md#architecture-overview) - **Complete system architecture**
- [Development Guidelines](Adventure_Chess_Glossary_v1-1-0.md#development-guidelines) - **Best practices for developers**
- [Canonical Abilities Reference](Adventure_Chess_Glossary_v1-1-0.md#canonical-abilities-reference) - **Complete ability documentation**
- [Configuration Options](Adventure_Chess_Glossary_v1-1-0.md#configuration-options-reference) - **All configuration parameters**
- [Testing Framework](Adventure_Chess_Glossary_v1-1-0.md#testing-framework) - **Comprehensive testing approach**
- [Troubleshooting](Adventure_Chess_Glossary_v1-1-0.md#troubleshooting) - **Common issues and solutions**
- [Version History](Adventure_Chess_Glossary_v1-1-0.md#version-history) - **Complete development timeline**
