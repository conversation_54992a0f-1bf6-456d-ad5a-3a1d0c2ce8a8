"""
Delay tag configuration for ability editor.
Handles delay ability configurations with turn and action delays.
"""

from PyQt6.QtWidgets import (QFormLayout, QCheckBox, QSpinBox, QHBoxLayout,
                            QWidget, QLabel, QVBoxLayout)
from PyQt6.QtCore import Qt
from typing import Dict, Any
from .base_tag_config import BaseTagConfig


class DelayConfig(BaseTagConfig):
    """Configuration for delay tag abilities."""
    
    def __init__(self, editor):
        super().__init__(editor, "delay")
    
    def create_ui(self, parent_layout) -> None:
        """
        Create the UI widgets for delay configuration matching old editor exactly.

        Args:
            parent_layout: The layout to add widgets to
        """
        try:
            self.log_debug("Starting delay UI creation")

            # Main widget
            main_widget = QWidget()
            layout = QVBoxLayout()

            # Description
            description = QLabel("Delays the ability's effect.")
            layout.addWidget(description)

            # Delay type selection
            delay_layout = QFormLayout()

            # Turn Delay
            turn_delay_layout = QHBoxLayout()
            delay_turn_check = QCheckBox("Turn Delay:")
            self.store_widget("delay_turn_check", delay_turn_check)
            turn_delay_layout.addWidget(delay_turn_check)
            
            delay_turn_spin = QSpinBox()
            delay_turn_spin.setRange(1, 20)
            delay_turn_spin.setValue(1)
            delay_turn_spin.setSuffix(" turns")
            delay_turn_spin.setEnabled(False)
            self.store_widget("delay_turn_spin", delay_turn_spin)
            turn_delay_layout.addWidget(delay_turn_spin)
            turn_delay_layout.addStretch()
            delay_layout.addRow("", turn_delay_layout)

            # Action Delay
            action_delay_layout = QHBoxLayout()
            delay_action_check = QCheckBox("Action Delay:")
            self.store_widget("delay_action_check", delay_action_check)
            action_delay_layout.addWidget(delay_action_check)
            
            delay_action_spin = QSpinBox()
            delay_action_spin.setRange(1, 20)
            delay_action_spin.setValue(1)
            delay_action_spin.setSuffix(" actions")
            delay_action_spin.setEnabled(False)
            self.store_widget("delay_action_spin", delay_action_spin)
            action_delay_layout.addWidget(delay_action_spin)
            action_delay_layout.addStretch()
            delay_layout.addRow("", action_delay_layout)

            # Connect checkboxes to enable/disable spinboxes
            delay_turn_check.stateChanged.connect(
                lambda state: delay_turn_spin.setEnabled(state == Qt.CheckState.Checked.value)
            )
            delay_action_check.stateChanged.connect(
                lambda state: delay_action_spin.setEnabled(state == Qt.CheckState.Checked.value)
            )

            layout.addLayout(delay_layout)
            main_widget.setLayout(layout)
            parent_layout.addWidget(main_widget)

            # Connect change signals
            self.connect_change_signals()

            self.log_debug("Delay UI creation completed")

        except Exception as e:
            self.log_error(f"Error creating UI: {e}")

    def populate_data(self, data: Dict[str, Any]) -> None:
        """
        Populate the UI widgets with data from an ability.

        Args:
            data: Dictionary containing ability data
        """
        try:
            self.log_debug("Populating delay data")

            # Populate turn delay
            delay_turn_check = self.get_widget_by_name("delay_turn_check")
            delay_turn_spin = self.get_widget_by_name("delay_turn_spin")
            if "delayTurns" in data:
                if delay_turn_check:
                    delay_turn_check.setChecked(True)
                if delay_turn_spin:
                    delay_turn_spin.setValue(data["delayTurns"])
                    delay_turn_spin.setEnabled(True)

            # Populate action delay
            delay_action_check = self.get_widget_by_name("delay_action_check")
            delay_action_spin = self.get_widget_by_name("delay_action_spin")
            if "delayActions" in data:
                if delay_action_check:
                    delay_action_check.setChecked(True)
                if delay_action_spin:
                    delay_action_spin.setValue(data["delayActions"])
                    delay_action_spin.setEnabled(True)

            self.log_debug("Delay data populated successfully")

        except Exception as e:
            self.log_error(f"Error populating data: {e}")

    def collect_data(self) -> Dict[str, Any]:
        """
        Collect data from the UI widgets.

        Returns:
            Dictionary containing the collected data
        """
        try:
            self.log_debug("Collecting delay data")
            data = {}

            # Collect turn delay
            delay_turn_check = self.get_widget_by_name("delay_turn_check")
            delay_turn_spin = self.get_widget_by_name("delay_turn_spin")
            if delay_turn_check and delay_turn_check.isChecked() and delay_turn_spin:
                data["delayTurns"] = delay_turn_spin.value()

            # Collect action delay
            delay_action_check = self.get_widget_by_name("delay_action_check")
            delay_action_spin = self.get_widget_by_name("delay_action_spin")
            if delay_action_check and delay_action_check.isChecked() and delay_action_spin:
                data["delayActions"] = delay_action_spin.value()

            self.log_debug("Delay data collected successfully")
            return data

        except Exception as e:
            self.log_error(f"Error collecting data: {e}")
            return {}
