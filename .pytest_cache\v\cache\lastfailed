{"tests/test_save_load_workflows.py::TestSimpleBridgeWorkflow::test_piece_ui_workflow_simulation": true, "tests/test_save_load_workflows.py::TestCrossManagerCompatibility::test_pydantic_to_direct_compatibility": true, "tests/test_main_app_integration.py::TestMainAppIntegration::test_lazy_patches_application": true, "tests/test_save_load_workflows.py::TestSaveLoadWorkflows": true, "tests/test_save_load_workflows.py::TestDirectDataManagerWorkflow": true, "tests/test_save_load_workflows.py::TestPydanticDataManagerWorkflow": true, "tests/test_save_load_workflows.py::TestSimpleBridgeWorkflow": true, "tests/test_save_load_workflows.py::TestCrossManagerCompatibility": true, "tests/test_save_load_workflows.py::TestPerformanceWorkflows": true, "tests/test_application_stress.py::TestApplicationStress::test_core_module_imports_stress": true, "tests/test_save_load_workflows.py::TestDirectDataManagerWorkflow::test_piece_save_load_cycle": true, "tests/test_save_load_workflows.py::TestDirectDataManagerWorkflow::test_ability_save_load_cycle": true, "tests/test_save_load_workflows.py::TestDirectDataManagerWorkflow::test_file_corruption_handling": true, "tests/test_save_load_workflows.py::TestPydanticDataManagerWorkflow::test_piece_validation_workflow": true, "tests/test_save_load_workflows.py::TestPydanticDataManagerWorkflow::test_ability_validation_workflow": true, "tests/test_save_load_workflows.py::TestPydanticDataManagerWorkflow::test_validation_error_handling": true, "tests/test_save_load_workflows.py::TestSimpleBridgeWorkflow::test_ability_ui_workflow_simulation": true, "tests/test_save_load_workflows.py::TestCrossManagerCompatibility::test_direct_to_pydantic_compatibility": true, "tests/test_save_load_workflows.py::TestPerformanceWorkflows::test_large_data_save_load": true, "tests/test_save_load_workflows.py::TestPerformanceWorkflows::test_concurrent_save_load": true, "tests/test_single_source_truth_verification.py::TestSingleSourceTruthVerification::test_core_module_structure": true}