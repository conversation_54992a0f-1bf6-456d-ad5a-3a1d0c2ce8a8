"""
PulseEffect tag configuration for ability editor.
Handles pulse effect ability configurations with interval settings.
"""

from PyQt6.QtWidgets import (QFormLayout, QSpinBox, QWidget, QLabel, QVBoxLayout)
from typing import Dict, Any
from .base_tag_config import BaseTagConfig


class PulseEffectConfig(BaseTagConfig):
    """Configuration for pulseEffect tag abilities."""
    
    def __init__(self, editor):
        super().__init__(editor, "pulseEffect")
    
    def create_ui(self, parent_layout) -> None:
        """
        Create the UI widgets for pulse effect configuration matching old editor exactly.

        Args:
            parent_layout: The layout to add widgets to
        """
        try:
            self.log_debug("Starting pulse effect UI creation")

            # Main widget
            main_widget = QWidget()
            layout = QVBoxLayout()

            # Description
            description = QLabel("Triggers the current ability on a recurring interval.")
            layout.addWidget(description)

            # Form layout for options
            form_layout = QFormLayout()

            # Only interval spinner (presence of tag = enabled) - Updated default per glossary V1.0.1
            pulse_interval_spin = QSpinBox()
            pulse_interval_spin.setRange(1, 20)
            pulse_interval_spin.setValue(1)  # Default should be 1 per glossary V1.0.1
            pulse_interval_spin.setSuffix(" turns")
            pulse_interval_spin.setToolTip("Repeat interval in turns")
            self.store_widget("pulse_interval_spin", pulse_interval_spin)
            form_layout.addRow("Repeat Every:", pulse_interval_spin)

            layout.addLayout(form_layout)
            main_widget.setLayout(layout)
            parent_layout.addWidget(main_widget)

            # Connect change signals
            self.connect_change_signals()

            self.log_debug("Pulse effect UI creation completed")

        except Exception as e:
            self.log_error(f"Error creating UI: {e}")

    def populate_data(self, data: Dict[str, Any]) -> None:
        """
        Populate the UI widgets with data from an ability.

        Args:
            data: Dictionary containing ability data
        """
        try:
            self.log_debug("Populating pulse effect data")

            # Populate interval
            pulse_interval_spin = self.get_widget_by_name("pulse_interval_spin")
            if pulse_interval_spin and "pulseInterval" in data:
                pulse_interval_spin.setValue(data["pulseInterval"])

            self.log_debug("Pulse effect data populated successfully")

        except Exception as e:
            self.log_error(f"Error populating data: {e}")

    def collect_data(self) -> Dict[str, Any]:
        """
        Collect data from the UI widgets.

        Returns:
            Dictionary containing the collected data
        """
        try:
            self.log_debug("Collecting pulse effect data")
            data = {}

            # Collect interval
            pulse_interval_spin = self.get_widget_by_name("pulse_interval_spin")
            if pulse_interval_spin:
                data["pulseInterval"] = pulse_interval_spin.value()

            self.log_debug("Pulse effect data collected successfully")
            return data

        except Exception as e:
            self.log_error(f"Error collecting data: {e}")
            return {}
