# Adventure Chess Creator - Core Module

## Overview

The `core/` directory contains the foundational architecture and systems that power the Adventure Chess Creator application. This module provides the base classes, interfaces, and specialized systems that all other components depend on.

## Directory Structure

```
core/
├── ui/                   # Core UI components and utilities
├── error_handling/       # Error handling and user-friendly error systems
├── security/             # Security validation and data protection
├── validation/           # Data validation rules and real-time validation
└── workflow/             # Template and workflow management systems
```

**Note**: The performance optimization system has been removed as it was unused in the application. The base_classes, handlers, managers, and interfaces directories were consolidated during the codebase cleanup.

## Key Components

### UI Components (`ui/`)
- **UnifiedTheme**: Consolidated theming system (replaces theme.py, theme_manager.py, theme_utils.py)
- **ConsolidatedUIUtils**: Unified UI utilities combining ui_utilities.py and ui_utils.py
- **InlineSelectionWidgets**: Reusable selection components for pieces and abilities
- **VisualFeedbackIntegration**: User feedback and notification systems
- **ResponsiveDesign**: Adaptive layouts for different screen sizes

### Error Handling (`error_handling/`)
- **StandardizedErrorDecorators**: Consistent error handling decorators for all modules
- **EnhancedErrorHandler**: Comprehensive error management system
- **UserFriendlyErrorDialog**: User-facing error presentation
- **ErrorMessageTranslator**: Technical error to user message translation

### Validation (`validation/`)
- **ValidationRules**: Data validation rules and real-time validation
- **SchemaValidation**: Pydantic schema integration
- **FieldValidation**: Individual field validation logic

### Security (`security/`)
- **SecurityPatterns**: Centralized security validation patterns (consolidates duplicate patterns)
- **SecurityValidator**: Data validation and security checks
- **SecureDataManager**: Secure file operations and data protection
- **CrashRecoveryManager**: Automatic backup and recovery systems

## Getting Started for New Developers

### 1. Understanding the Architecture

The core module follows a layered architecture:
- **Base Layer**: Abstract classes defining common interfaces
- **Implementation Layer**: Concrete implementations of base functionality
- **Specialization Layer**: Domain-specific handlers and managers
- **Integration Layer**: Systems that tie everything together

### 2. Key Design Patterns

- **Single Source of Truth**: All data operations go through centralized handlers
- **Lazy Loading**: Components are loaded only when needed for performance
- **Error Boundaries**: Comprehensive error handling at all levels
- **Modular Design**: Each subsystem is independent and testable

### 3. Working with Base Classes

When creating new components, always extend the appropriate base class:

```python
from core.base_classes.base_editor import BaseEditor
from core.base_classes.base_data_handler import BaseDataHandler

class MyNewEditor(BaseEditor):
    def __init__(self):
        super().__init__(data_type="my_data_type")
        # Your implementation
```

### 4. Data Management Best Practices

- Always use the enhanced data handlers for file operations
- Implement proper error handling using the core error system
- Use the validation system for all user input
- Follow the single source of truth principle

### 5. UI Development Guidelines

- Extend BaseWidget for all custom UI components
- Use the core UI utilities for consistent styling
- Implement proper signal/slot patterns for UI updates
- Follow the established widget patterns throughout the application

## Upgrading the Codebase

### Adding New Features

1. **Identify the appropriate subsystem** (handlers, managers, ui, etc.)
2. **Create base classes** if new patterns are needed
3. **Implement concrete classes** extending the base functionality
4. **Add proper error handling** using the core error system
5. **Include validation** using the validation framework
6. **Write tests** for all new functionality

### Modifying Existing Components

1. **Check dependencies** before making changes
2. **Update base classes** if interface changes are needed
3. **Maintain backward compatibility** when possible
4. **Update all implementations** that depend on changed interfaces
5. **Run comprehensive tests** to ensure no regressions

### Performance Considerations

- Use the lazy loading system for expensive operations
- Leverage the caching system for frequently accessed data
- Monitor performance using the integrated performance tools
- Consider file system optimization for large datasets

### Security Guidelines

- Always validate user input using the security validator
- Use secure data manager for sensitive operations
- Implement proper backup and recovery mechanisms
- Follow the established security patterns

## Testing Strategy

### Unit Testing
- Test all base classes with mock implementations
- Test data handlers with sample data
- Test UI components in isolation

### Integration Testing
- Test complete workflows end-to-end
- Test error handling scenarios
- Test performance under load

### Validation Testing
- Test all validation rules
- Test error message translation
- Test user-friendly error presentation

## Common Patterns

### Data Loading Pattern
```python
from core.handlers.specialized_data_handlers import EnhancedPieceDataHandler

handler = EnhancedPieceDataHandler()
data = handler.load_data("piece_name")
```

### Error Handling Pattern
```python
import logging

logger = logging.getLogger(__name__)

try:
    # Your operation
    pass
except Exception as e:
    logger.error(f"Error in your_operation_name: {e}")
    # Handle error appropriately for your use case
```

### UI Widget Pattern
```python
from core.base_classes.base_widgets import BaseFormWidget

class MyWidget(BaseFormWidget):
    def create_ui(self):
        # Create your UI elements
        pass
    
    def populate_data(self, data):
        # Populate UI with data
        pass
    
    def collect_data(self):
        # Collect data from UI
        return data
```

## Troubleshooting

### Common Issues

1. **Import Errors**: Ensure all dependencies are properly installed
2. **Data Loading Issues**: Check file permissions and data format
3. **UI Rendering Issues**: Verify PyQt6 installation and dependencies
4. **Performance Issues**: Use the performance monitoring tools

### Debug Mode

Enable debug logging for detailed troubleshooting:
```python
import logging
logging.getLogger().setLevel(logging.DEBUG)
```

### Getting Help

- Check the error logs in `utils/logs/`
- Use the built-in error reporting system
- Refer to the glossary documentation for terminology
- Review the test files for usage examples
- See `docs/ERROR_HANDLING_STANDARDS.md` for error handling guidelines
- See `docs/IMPORT_OPTIMIZATION_GUIDE.md` for import best practices
- See `docs/theming_guide.md` for unified theming system usage

## Contributing

When contributing to the core module:

1. Follow the established patterns and conventions
2. Add comprehensive documentation for new components
3. Include unit tests for all new functionality
4. Update this README if adding new subsystems
5. Ensure backward compatibility when possible

## Version History

- **v1.1.0**:
  - Consolidated theming system into UnifiedTheme
  - Merged UI utilities into ConsolidatedUIUtils
  - Centralized security patterns in SecurityPatterns
  - Standardized error handling with decorators
  - Optimized import structure and documentation
- **v1.0.0**: Initial modular architecture implementation

---

For more detailed information about specific components, refer to the individual module documentation and the project glossary.
