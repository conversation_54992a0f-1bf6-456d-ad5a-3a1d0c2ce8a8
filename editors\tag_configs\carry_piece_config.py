"""
Carry Piece Tag Configuration for Adventure Chess Creator

This module handles the UI creation and data management for the carryPiece ability tag.
Matches the old ability editor exactly with all nested UI elements and custom range patterns.

Fields handled:
- carryTargets (InlinePieceSelector): List of pieces that can be carried
- carryRange (QSpinBox): Range for carrying pieces (0-8, where 0=self)
- carryRangePattern: Custom range pattern for carrying
- carryDropOnDeath (QCheckBox): Whether to drop carried pieces on death
- carryDropMode (QComboBox): How dropped pieces are placed (random/selectable)
- carryDropRange (QSpinBox): Range for dropping pieces
- carryDropRangePattern: Custom drop range pattern
- carryDropCanCapture (QCheckBox): Whether dropped pieces can capture
- carryShareAbilities (QCheckBox): Whether carried pieces share abilities
- carryStartingPiece (QCheckBox): Whether piece starts carrying another piece
"""

import logging
from typing import Dict, Any
from PyQt6.QtWidgets import (QWidget, QSpinBox, QCheckBox, QVBoxLayout, QHBoxLayout,
                             QFormLayout, QLabel, QPushButton, QComboBox)
from PyQt6.QtCore import Qt

from .base_tag_config import BaseTagConfig
from dialogs.inline_selectors import InlinePieceSelector

logger = logging.getLogger(__name__)


class CarryPieceConfig(BaseTagConfig):
    """Configuration handler for the carryPiece ability tag."""

    def __init__(self, editor_instance):
        super().__init__(editor_instance, "carryPiece")

    def get_title(self) -> str:
        """Get the display title for carry piece configuration."""
        return "🎒 Carry Piece Configuration"
    
    def create_ui(self, parent_layout) -> None:
        """
        Create the UI widgets for carry piece configuration matching old editor exactly.

        Args:
            parent_layout: The layout to add widgets to
        """
        try:
            self.log_debug("Starting UI creation")

            # Main layout
            layout = QVBoxLayout()

            # Description
            description = QLabel("Allow this piece to carry other pieces.")
            layout.addWidget(description)

            # Enhanced inline piece selector for carry targets
            carry_selector = InlinePieceSelector(self.editor, "Carry Targets", allow_costs=True)
            self.store_widget("carry_target_selector", carry_selector)
            layout.addWidget(carry_selector)

            # Enhanced carry options
            options_layout = QFormLayout()

            # Carry range (updated per glossary: use range editor for custom patterns)
            carry_range_layout = QHBoxLayout()
            carry_range_spin = QSpinBox()
            carry_range_spin.setRange(0, 8)
            carry_range_spin.setValue(0)
            carry_range_spin.setToolTip("Distance for picking up pieces (0=self, 1-8=range)")
            carry_range_layout.addWidget(carry_range_spin)
            self.store_widget("carry_range_spin", carry_range_spin)

            carry_range_custom_btn = QPushButton("Custom Range")
            carry_range_custom_btn.clicked.connect(self.edit_carry_range_pattern)
            carry_range_custom_btn.setToolTip("Define custom carry range pattern")
            carry_range_layout.addWidget(carry_range_custom_btn)
            carry_range_layout.addStretch()
            self.store_widget("carry_range_custom_btn", carry_range_custom_btn)
            options_layout.addRow("Carry Range:", carry_range_layout)

            # Drop options (new per glossary)
            carry_drop_on_death_check = QCheckBox("Drop carried pieces when carrier dies")
            carry_drop_on_death_check.stateChanged.connect(self.on_carry_drop_on_death_changed)
            self.store_widget("carry_drop_on_death_check", carry_drop_on_death_check)
            options_layout.addRow("Drop on Death:", carry_drop_on_death_check)

            carry_drop_mode_combo = QComboBox()
            carry_drop_mode_combo.addItems(["random", "selectable"])  # Removed "self"
            carry_drop_mode_combo.setToolTip("How dropped pieces are placed")
            carry_drop_mode_combo.setVisible(False)  # Hidden by default
            self.store_widget("carry_drop_mode_combo", carry_drop_mode_combo)
            options_layout.addRow("Drop Mode:", carry_drop_mode_combo)

            # Drop range with custom option (updated per glossary)
            drop_range_layout = QHBoxLayout()
            carry_drop_range_spin = QSpinBox()
            carry_drop_range_spin.setRange(0, 10)  # Updated to 0-10 range
            carry_drop_range_spin.setValue(1)
            carry_drop_range_spin.setToolTip("Distance for dropping pieces (0-10, or use custom)")
            drop_range_layout.addWidget(carry_drop_range_spin)
            self.store_widget("carry_drop_range_spin", carry_drop_range_spin)

            carry_drop_range_custom_btn = QPushButton("Custom Drop Range")
            carry_drop_range_custom_btn.clicked.connect(self.edit_carry_drop_range_pattern)
            carry_drop_range_custom_btn.setToolTip("Define custom drop range pattern")
            drop_range_layout.addWidget(carry_drop_range_custom_btn)
            drop_range_layout.addStretch()
            self.store_widget("carry_drop_range_custom_btn", carry_drop_range_custom_btn)

            # Initially hide drop range options
            carry_drop_range_widget = QWidget()
            carry_drop_range_widget.setLayout(drop_range_layout)
            carry_drop_range_widget.setVisible(False)
            self.store_widget("carry_drop_range_widget", carry_drop_range_widget)
            options_layout.addRow("Drop Range:", carry_drop_range_widget)

            carry_drop_can_capture_check = QCheckBox("Dropped pieces can capture")
            carry_drop_can_capture_check.setVisible(False)  # Hidden by default
            self.store_widget("carry_drop_can_capture_check", carry_drop_can_capture_check)
            options_layout.addRow("Drop Capture:", carry_drop_can_capture_check)

            # Max carry spinner
            max_carry_spin = QSpinBox()
            max_carry_spin.setRange(1, 10)
            max_carry_spin.setValue(1)
            max_carry_spin.setToolTip("Maximum number of pieces that can be carried simultaneously")
            self.store_widget("max_carry_spin", max_carry_spin)
            options_layout.addRow("Max Carry:", max_carry_spin)

            # Share Abilities
            carry_share_abilities_check = QCheckBox("Share Abilities")
            carry_share_abilities_check.setToolTip("Carried piece can use abilities while being carried")
            self.store_widget("carry_share_abilities_check", carry_share_abilities_check)
            options_layout.addRow("Abilities:", carry_share_abilities_check)

            # Starting piece selector
            starting_piece_selector = InlinePieceSelector(self.editor, "Starting Piece", allow_costs=False)
            starting_piece_selector.setToolTip("Select piece that this piece starts carrying")
            self.store_widget("starting_piece_selector", starting_piece_selector)
            options_layout.addRow("Starting Piece:", starting_piece_selector)

            layout.addLayout(options_layout)

            # Add the main layout to the parent
            main_widget = QWidget()
            main_widget.setLayout(layout)
            parent_layout.addWidget(main_widget)

            # Connect change signals for all widgets
            self.connect_change_signals(carry_range_spin)
            self.connect_change_signals(carry_drop_on_death_check)
            self.connect_change_signals(carry_drop_mode_combo)
            self.connect_change_signals(carry_drop_range_spin)
            self.connect_change_signals(carry_drop_can_capture_check)
            self.connect_change_signals(max_carry_spin)
            self.connect_change_signals(carry_share_abilities_check)
            self.connect_change_signals(starting_piece_selector)

            self.log_debug("UI created successfully")

        except Exception as e:
            self.log_error(f"Error creating UI: {e}")
            raise

    def on_carry_drop_on_death_changed(self, state):
        """Handle carry drop on death checkbox toggle"""
        enabled = state == Qt.CheckState.Checked.value

        drop_mode_combo = self.get_widget_by_name('carry_drop_mode_combo')
        if drop_mode_combo:
            drop_mode_combo.setVisible(enabled)

        drop_range_widget = self.get_widget_by_name('carry_drop_range_widget')
        if drop_range_widget:
            drop_range_widget.setVisible(enabled)

        drop_can_capture_check = self.get_widget_by_name('carry_drop_can_capture_check')
        if drop_can_capture_check:
            drop_can_capture_check.setVisible(enabled)

    def edit_carry_range_pattern(self):
        """Edit carry range pattern using range editor dialog"""
        from dialogs.range_editor_dialog import edit_range_pattern

        # Get initial pattern if exists
        initial_pattern = getattr(self, 'carry_range_pattern', None)
        initial_position = getattr(self, 'carry_range_piece_position', [3, 3])
        checkbox_states = getattr(self, 'carry_range_checkbox_states', {})

        pattern, piece_position, new_checkbox_states = edit_range_pattern(
            initial_pattern, initial_position,
            "Edit Carry Range Pattern", self.editor, checkbox_states
        )

        if pattern is not None:
            self.carry_range_pattern = pattern
            self.carry_range_piece_position = piece_position
            self.carry_range_checkbox_states = new_checkbox_states
            from PyQt6.QtWidgets import QMessageBox
            QMessageBox.information(self.editor, "Pattern Saved", "Carry range pattern has been updated.")

    def edit_carry_drop_range_pattern(self):
        """Edit carry drop range pattern using range editor dialog"""
        from dialogs.range_editor_dialog import edit_range_pattern

        # Get initial pattern if exists
        initial_pattern = getattr(self, 'carry_drop_range_pattern', None)
        initial_position = getattr(self, 'carry_drop_range_piece_position', [3, 3])
        checkbox_states = getattr(self, 'carry_drop_range_checkbox_states', {})

        pattern, piece_position, new_checkbox_states = edit_range_pattern(
            initial_pattern, initial_position,
            "Edit Carry Drop Range Pattern", self.editor, checkbox_states
        )

        if pattern is not None:
            self.carry_drop_range_pattern = pattern
            self.carry_drop_range_piece_position = piece_position
            self.carry_drop_range_checkbox_states = new_checkbox_states
            from PyQt6.QtWidgets import QMessageBox
            QMessageBox.information(self.editor, "Pattern Saved", "Carry drop range pattern has been updated.")

    def populate_data(self, data: Dict[str, Any]) -> None:
        """
        Populate the UI widgets with data from an ability.

        Args:
            data: Dictionary containing ability data
        """
        try:
            self.log_debug(f"Starting data population with: {data}")

            # Populate carry targets (using both old and new field names)
            carry_selector = self.get_widget_by_name("carry_target_selector")
            if carry_selector:
                # Try both field names for compatibility
                carry_data = data.get("carryTargets", data.get("carryList", data.get("carry_targets", [])))
                if carry_data:
                    self.log_debug(f"Populating carry targets: {carry_data}")
                    carry_selector.set_pieces(carry_data)

            # Populate carry range
            carry_range = self.get_widget_by_name("carry_range_spin")
            if carry_range:
                range_value = data.get("carryRange", 0)
                self.log_debug(f"Setting carry range to: {range_value}")
                carry_range.setValue(range_value)

            # Populate drop on death checkbox and trigger visibility changes
            drop_check = self.get_widget_by_name("carry_drop_on_death_check")
            if drop_check:
                drop_on_death = data.get("carryDropOnDeath", False)
                drop_check.setChecked(drop_on_death)
                # Trigger visibility changes
                self.on_carry_drop_on_death_changed(Qt.CheckState.Checked if drop_on_death else Qt.CheckState.Unchecked)

            # Populate drop mode
            drop_mode_combo = self.get_widget_by_name("carry_drop_mode_combo")
            if drop_mode_combo:
                drop_mode = data.get("carryDropMode", "random")
                index = drop_mode_combo.findText(drop_mode)
                if index >= 0:
                    drop_mode_combo.setCurrentIndex(index)

            # Populate drop range
            drop_range_spin = self.get_widget_by_name("carry_drop_range_spin")
            if drop_range_spin:
                drop_range_spin.setValue(data.get("carryDropRange", 1))

            # Populate drop can capture
            drop_capture_check = self.get_widget_by_name("carry_drop_can_capture_check")
            if drop_capture_check:
                drop_capture_check.setChecked(data.get("carryDropCanCapture", False))

            # Populate max carry
            max_carry_spin = self.get_widget_by_name("max_carry_spin")
            if max_carry_spin:
                max_carry_spin.setValue(data.get("carryMaxPieces", 1))

            # Populate share abilities
            share_check = self.get_widget_by_name("carry_share_abilities_check")
            if share_check:
                share_check.setChecked(data.get("carryShareAbilities", False))

            # Populate starting piece selector
            starting_selector = self.get_widget_by_name("starting_piece_selector")
            if starting_selector and "carryStartingPieces" in data:
                starting_selector.set_pieces(data["carryStartingPieces"])

            # Load custom patterns if they exist
            if "carryRangePattern" in data:
                self.carry_range_pattern = data["carryRangePattern"]
            if "carryRangePiecePosition" in data:
                self.carry_range_piece_position = data["carryRangePiecePosition"]
            if "carryRangeCheckboxStates" in data:
                self.carry_range_checkbox_states = data["carryRangeCheckboxStates"]

            if "carryDropRangePattern" in data:
                self.carry_drop_range_pattern = data["carryDropRangePattern"]
            if "carryDropRangePiecePosition" in data:
                self.carry_drop_range_piece_position = data["carryDropRangePiecePosition"]
            if "carryDropRangeCheckboxStates" in data:
                self.carry_drop_range_checkbox_states = data["carryDropRangeCheckboxStates"]

            self.log_debug("Data populated successfully")

        except Exception as e:
            self.log_error(f"Error populating data: {e}")
    
    def collect_data(self) -> Dict[str, Any]:
        """
        Collect data from the UI widgets.

        Returns:
            Dictionary containing the carry piece configuration data
        """
        try:
            data = {}

            # Collect carry targets
            carry_selector = self.get_widget_by_name("carry_target_selector")
            if carry_selector:
                pieces = carry_selector.get_pieces()
                if pieces:
                    data["carryTargets"] = pieces

            # Collect carry range
            carry_range = self.get_widget_by_name("carry_range_spin")
            if carry_range:
                data["carryRange"] = carry_range.value()

            # Collect drop on death
            drop_check = self.get_widget_by_name("carry_drop_on_death_check")
            if drop_check and drop_check.isChecked():
                data["carryDropOnDeath"] = True

                # Collect drop mode
                drop_mode_combo = self.get_widget_by_name("carry_drop_mode_combo")
                if drop_mode_combo:
                    data["carryDropMode"] = drop_mode_combo.currentText()

                # Collect drop range
                drop_range_spin = self.get_widget_by_name("carry_drop_range_spin")
                if drop_range_spin:
                    data["carryDropRange"] = drop_range_spin.value()

                # Collect drop can capture
                drop_capture_check = self.get_widget_by_name("carry_drop_can_capture_check")
                if drop_capture_check and drop_capture_check.isChecked():
                    data["carryDropCanCapture"] = True

            # Collect max carry
            max_carry_spin = self.get_widget_by_name("max_carry_spin")
            if max_carry_spin:
                data["carryMaxPieces"] = max_carry_spin.value()

            # Collect share abilities
            share_check = self.get_widget_by_name("carry_share_abilities_check")
            if share_check and share_check.isChecked():
                data["carryShareAbilities"] = True

            # Collect starting pieces
            starting_selector = self.get_widget_by_name("starting_piece_selector")
            if starting_selector:
                starting_pieces = starting_selector.get_pieces()
                if starting_pieces:
                    data["carryStartingPieces"] = starting_pieces

            # Collect custom patterns if they exist
            if hasattr(self, 'carry_range_pattern'):
                data["carryRangePattern"] = self.carry_range_pattern
            if hasattr(self, 'carry_range_piece_position'):
                data["carryRangePiecePosition"] = self.carry_range_piece_position
            if hasattr(self, 'carry_range_checkbox_states'):
                data["carryRangeCheckboxStates"] = self.carry_range_checkbox_states

            if hasattr(self, 'carry_drop_range_pattern'):
                data["carryDropRangePattern"] = self.carry_drop_range_pattern
            if hasattr(self, 'carry_drop_range_piece_position'):
                data["carryDropRangePiecePosition"] = self.carry_drop_range_piece_position
            if hasattr(self, 'carry_drop_range_checkbox_states'):
                data["carryDropRangeCheckboxStates"] = self.carry_drop_range_checkbox_states

            self.log_debug(f"Data collected: {data}")
            return data

        except Exception as e:
            self.log_error(f"Error collecting data: {e}")
            return {}
