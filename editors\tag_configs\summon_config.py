"""
Summon tag configuration for ability editor.
Handles summon-based ability configurations.
"""

from PyQt6.QtWidgets import QSpin<PERSON>ox, QWidget
from typing import Dict, Any
from .base_tag_config import BaseTagConfig
from core.ui import InlinePieceSelector


class SummonConfig(BaseTagConfig):
    """Configuration for summon tag abilities."""
    
    def __init__(self, editor):
        super().__init__(editor, "summon")
    
    def create_ui(self, parent_layout) -> None:
        """
        Create the UI widgets for summon configuration.
        
        Args:
            parent_layout: The layout to add widgets to
        """
        try:
            self.log_debug("Starting summon UI creation")
            
            # Create form layout
            form_layout = self.create_form_layout()
            self.log_debug("Created form layout")
            
            # Summon pieces selector (InlinePieceSelector)
            summon_selector = InlinePieceSelector(
                parent=self.editor,
                title="Summon Targets",
                allow_costs=True
            )
            self.store_widget("summon_piece_selector", summon_selector)
            form_layout.addRow("Pieces to Summon:", summon_selector)
            self.log_debug("Added summon piece selector")
            
            # Maximum summons per use spinner (1-10)
            summon_max = QSpinBox()
            summon_max.setRange(1, 10)
            summon_max.setValue(1)
            summon_max.setToolTip("Maximum number of pieces that can be summoned per use")
            self.store_widget("summon_max_spinner", summon_max)
            self.connect_change_signals(summon_max)
            form_layout.addRow("Max Summons:", summon_max)
            self.log_debug("Added summon max spinner")
            
            # Add the form layout to the parent
            form_widget = QWidget()
            form_widget.setLayout(form_layout)
            parent_layout.addWidget(form_widget)
            
            self.log_debug("Summon UI creation completed")
            
        except Exception as e:
            self.log_error(f"Error creating UI: {e}")
    
    def populate_data(self, data: Dict[str, Any]) -> None:
        """
        Populate the UI widgets with data from an ability.
        
        Args:
            data: Dictionary containing ability data
        """
        try:
            self.log_debug(f"Starting summon data population with: {data}")
            
            # Populate summon pieces list
            summon_selector = self.get_widget_by_name("summon_piece_selector")
            if summon_selector:
                summon_list = data.get("summonTargets", [])
                self.log_debug(f"Setting summon list to: {summon_list}")
                summon_selector.set_pieces(summon_list)
            else:
                self.log_debug("No summon piece selector widget found")
            
            # Populate summon max
            summon_max = self.get_widget_by_name("summon_max_spinner")
            if summon_max:
                max_value = data.get("summonMax", 1)
                self.log_debug(f"Setting summon max to: {max_value}")
                summon_max.setValue(max_value)
            else:
                self.log_debug("No summon max widget found")
            
            self.log_debug("Summon data populated successfully")
            
        except Exception as e:
            self.log_error(f"Error populating data: {e}")
    
    def collect_data(self) -> Dict[str, Any]:
        """
        Collect data from the UI widgets.
        
        Returns:
            Dictionary containing the summon configuration data
        """
        try:
            data = {}
            
            # Collect summon pieces list
            summon_selector = self.get_widget_by_name("summon_piece_selector")
            if summon_selector:
                pieces_list = summon_selector.get_pieces()
                data["summonTargets"] = pieces_list
                self.log_debug(f"Collected summon list: {pieces_list}")
            
            # Collect summon max
            summon_max = self.get_widget_by_name("summon_max_spinner")
            if summon_max:
                max_value = summon_max.value()
                data["summonMax"] = max_value
                self.log_debug(f"Collected summon max: {max_value}")
            
            self.log_debug(f"Collected summon data: {data}")
            return data
            
        except Exception as e:
            self.log_error(f"Error collecting data: {e}")
            return {}
