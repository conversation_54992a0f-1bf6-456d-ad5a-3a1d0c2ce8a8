"""
Invisible tag configuration for the Adventure Chess Creator ability editor.
Makes pieces invisible until certain conditions are met.
"""

from typing import Dict, Any
from PyQt6.QtWidgets import (
    QVBoxLayout, QHBoxLayout, QFormLayout, QGroupBox, QLabel, QSpinBox, QComboBox
)
from PyQt6.QtCore import Qt

from .base_tag_config import BaseTagConfig


class InvisibleConfig(BaseTagConfig):
    """Configuration for invisible tag - piece invisibility with reveal conditions."""
    
    def __init__(self, editor_instance):
        super().__init__(editor_instance, "invisible")
    
    def get_title(self) -> str:
        return "👻 Invisible Configuration"
    
    def create_ui(self, parent_layout) -> None:
        """Create the invisible configuration UI."""
        try:
            # Main group box
            group = QGroupBox(self.get_title())
            layout = QVBoxLayout()
            
            # Description
            description = QLabel("Makes piece invisible until certain conditions are met.")
            description.setWordWrap(True)
            layout.addWidget(description)
            
            # Reveal conditions
            reveal_layout = QFormLayout()

            # Reveal condition dropdown
            reveal_combo = QComboBox()
            reveal_combo.addItems([
                "Never reveal (permanent invisibility)",
                "Reveal after moving",
                "Reveal when attacking",
                "Reveal when attacked",
                "Reveal when using abilities",
                "Reveal when enemy within range",
                "Reveal when in enemy line of sight"
            ])
            reveal_combo.setToolTip("Select when the piece becomes visible")
            self.store_widget("invisible_reveal_combo", reveal_combo)
            reveal_layout.addRow("Reveal Condition:", reveal_combo)

            # Value spinner (for conditions that need a number)
            reveal_value_spin = QSpinBox()
            reveal_value_spin.setRange(1, 10)
            reveal_value_spin.setValue(1)
            reveal_value_spin.setToolTip("Number of moves/actions or distance for reveal condition")
            reveal_value_spin.setVisible(False)  # Hidden by default
            self.store_widget("invisible_reveal_value_spin", reveal_value_spin)
            reveal_layout.addRow("Value:", reveal_value_spin)

            # Connect combo to show/hide value spinner
            reveal_combo.currentTextChanged.connect(self.on_reveal_condition_changed)

            layout.addLayout(reveal_layout)

            
            group.setLayout(layout)
            parent_layout.addWidget(group)
            
            self.log_debug("Invisible configuration UI created successfully")

        except Exception as e:
            self.log_error(f"Error creating invisible UI: {e}")

    def on_reveal_condition_changed(self, condition_text):
        """Handle reveal condition dropdown change to show/hide value spinner."""
        try:
            reveal_value_spin = self.get_widget_by_name("invisible_reveal_value_spin")
            if reveal_value_spin:
                # Show value spinner for conditions that need a number
                needs_value = any(keyword in condition_text.lower() for keyword in
                                ["moving", "within range"])
                reveal_value_spin.setVisible(needs_value)

                # Update tooltip based on condition
                if "moving" in condition_text.lower():
                    reveal_value_spin.setToolTip("Number of moves before revealing")
                    reveal_value_spin.setSuffix(" moves")
                elif "within range" in condition_text.lower():
                    reveal_value_spin.setToolTip("Distance at which enemies reveal the piece")
                    reveal_value_spin.setSuffix(" squares")
                else:
                    reveal_value_spin.setSuffix("")

        except Exception as e:
            self.log_error(f"Error handling reveal condition change: {e}")
    
    def populate_data(self, data: Dict[str, Any]) -> None:
        """Populate the UI with data from an ability."""
        try:
            self.log_debug(f"Populating invisible data: {data}")

            # Populate reveal condition dropdown
            reveal_combo = self.get_widget_by_name("invisible_reveal_combo")
            reveal_value_spin = self.get_widget_by_name("invisible_reveal_value_spin")

            if reveal_combo:
                # Map data to dropdown selection
                reveal_type = data.get("invisibleRevealType", "never")
                reveal_value = data.get("invisibleRevealValue", 1)

                if reveal_type == "never":
                    reveal_combo.setCurrentIndex(0)
                elif reveal_type == "onMove":
                    reveal_combo.setCurrentIndex(1)
                    if reveal_value_spin:
                        reveal_value_spin.setValue(reveal_value)
                elif reveal_type == "onAttack":
                    reveal_combo.setCurrentIndex(2)
                elif reveal_type == "whenAttacked":
                    reveal_combo.setCurrentIndex(3)
                elif reveal_type == "onAbilityUse":
                    reveal_combo.setCurrentIndex(4)
                elif reveal_type == "onProximity":
                    reveal_combo.setCurrentIndex(5)
                    if reveal_value_spin:
                        reveal_value_spin.setValue(reveal_value)
                elif reveal_type == "onEnemyLoS":
                    reveal_combo.setCurrentIndex(6)

                # Trigger the change handler to show/hide value spinner
                self.on_reveal_condition_changed(reveal_combo.currentText())

            self.log_debug("Invisible data populated successfully")

        except Exception as e:
            self.log_error(f"Error populating invisible data: {e}")
    
    def collect_data(self) -> Dict[str, Any]:
        """Collect data from the UI widgets."""
        try:
            data = {}

            # Collect reveal condition
            reveal_combo = self.get_widget_by_name("invisible_reveal_combo")
            reveal_value_spin = self.get_widget_by_name("invisible_reveal_value_spin")

            if reveal_combo:
                current_index = reveal_combo.currentIndex()
                reveal_value = reveal_value_spin.value() if reveal_value_spin else 1

                if current_index == 0:  # Never reveal
                    data["invisibleRevealType"] = "never"
                elif current_index == 1:  # Reveal after moving
                    data["invisibleRevealType"] = "onMove"
                    data["invisibleRevealValue"] = reveal_value
                elif current_index == 2:  # Reveal when attacking
                    data["invisibleRevealType"] = "onAttack"
                elif current_index == 3:  # Reveal when attacked
                    data["invisibleRevealType"] = "whenAttacked"
                elif current_index == 4:  # Reveal when using abilities
                    data["invisibleRevealType"] = "onAbilityUse"
                elif current_index == 5:  # Reveal when enemy within range
                    data["invisibleRevealType"] = "onProximity"
                    data["invisibleRevealValue"] = reveal_value
                elif current_index == 6:  # Reveal when in enemy line of sight
                    data["invisibleRevealType"] = "onEnemyLoS"

            self.log_debug(f"Collected invisible data: {data}")
            return data

        except Exception as e:
            self.log_error(f"Error collecting invisible data: {e}")
            return {}
