"""
Utility modules for Adventure Chess

Location: utils/

Contains general utilities, validators, and helper functions:
- utils.py: Core utility functions (file I/O, logging, validation)
- simple_bridge.py: Data loading interface for UI components
- readable_json_formatter.py: Custom JSON formatting for movement patterns
- batch_update.py: Removed - was specialized maintenance feature

Used by: All modules for common operations and data access
"""
