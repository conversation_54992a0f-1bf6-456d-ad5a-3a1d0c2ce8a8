"""
Move tag configuration for ability editor.
Handles movement-based ability configurations.
"""

from PyQt6.QtWidgets import <PERSON><PERSON>abel, QVBoxLayout, QWidget
from typing import Dict, Any
from .base_tag_config import BaseTagConfig


class MoveConfig(BaseTagConfig):
    """Configuration for move tag abilities."""
    
    def __init__(self, editor):
        super().__init__(editor, "move")
    
    def create_ui(self, parent_layout) -> None:
        """
        Create the UI widgets for move configuration (simplified - no options).

        Args:
            parent_layout: The layout to add widgets to
        """
        try:
            self.log_debug("Starting move UI creation")

            # Main widget
            main_widget = QWidget()
            layout = QVBoxLayout()

            # Description only - no configuration options
            description = QLabel("Grants movement abilities to the piece.")
            description.setWordWrap(True)
            description.setStyleSheet("color: #666; font-style: italic; margin: 10px;")
            layout.addWidget(description)

            main_widget.setLayout(layout)
            parent_layout.addWidget(main_widget)

            self.log_debug("Move UI creation completed")

        except Exception as e:
            self.log_error(f"Error creating UI: {e}")
    
    def populate_data(self, data: Dict[str, Any]) -> None:
        """
        Populate the UI widgets with data from an ability (simplified - no options).

        Args:
            data: Dictionary containing ability data
        """
        try:
            self.log_debug(f"Starting move data population with: {data}")
            # No UI widgets to populate since all options were removed
            self.log_debug("Move data populated successfully")

        except Exception as e:
            self.log_error(f"Error populating data: {e}")
    
    def collect_data(self) -> Dict[str, Any]:
        """
        Collect data from the UI widgets (simplified - no options).

        Returns:
            Dictionary containing the move configuration data
        """
        try:
            data = {}
            # No UI widgets to collect from since all options were removed
            # Just return empty dict to indicate move tag is present

            self.log_debug(f"Collected move data: {data}")
            return data

        except Exception as e:
            self.log_error(f"Error collecting data: {e}")
            return {}
