"""
Requires Starting Position tag configuration for the Adventure Chess Creator ability editor.
Restricts ability activation to pieces still in their starting position.
"""

from typing import Dict, Any
from .base_tag_config import BaseTagConfig


class RequiresStartingPositionConfig(BaseTagConfig):
    """Configuration for requiresStartingPosition tag - starting position requirement."""
    
    def __init__(self, editor_instance):
        super().__init__(editor_instance, "requiresStartingPosition")
    
    def get_title(self) -> str:
        return "🏠 Starting Position Requirement"
    
    def create_ui(self, parent_layout) -> None:
        """Create the requires starting position configuration UI using standardized patterns."""
        try:
            # Create main layout with description
            main_layout = self.create_standard_layout(
                parent_layout,
                "This ability can only be activated if the piece is still in its starting position."
            )

            # Information section
            info_layout = self.create_vertical_section(main_layout, "Information")

            # Additional explanation using standardized description
            explanation_label = self.create_description_label(
                "No additional configuration is required for this tag. "
                "The game engine will automatically check if the piece has moved from its initial position."
            )
            info_layout.addWidget(explanation_label)

            self.log_debug("Requires starting position configuration UI created successfully using standardized patterns")

        except Exception as e:
            self.log_error(f"Error creating requires starting position UI: {e}")
    
    def populate_data(self, data: Dict[str, Any]) -> None:
        """Populate the UI with data from an ability."""
        try:
            self.log_debug(f"Populating requires starting position data: {data}")
            # This tag has no configurable options, so no data to populate
            self.log_debug("Requires starting position data populated successfully (no options)")
            
        except Exception as e:
            self.log_error(f"Error populating requires starting position data: {e}")
    
    def collect_data(self) -> Dict[str, Any]:
        """Collect data from the UI widgets."""
        try:
            # This tag has no configurable options, just return empty dict
            # The presence of the tag in the ability is sufficient
            data = {}
            
            self.log_debug(f"Collected requires starting position data: {data}")
            return data
            
        except Exception as e:
            self.log_error(f"Error collecting requires starting position data: {e}")
            return {}
