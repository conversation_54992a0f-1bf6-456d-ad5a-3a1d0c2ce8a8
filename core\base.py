"""
Core Base Classes for Adventure Chess Creator

Consolidated base classes to reduce complexity and duplication.
All base classes in one place for easier maintenance.
"""

import logging
from abc import ABC, abstractmethod
from typing import Any, Dict, List, Optional

from PyQt6.QtWidgets import QWidget, QVBoxLayout, QHBoxLayout, QFormLayout
from PyQt6.QtCore import QObject, pyqtSignal

logger = logging.getLogger(__name__)


# ========== BASE WIDGET CLASSES ==========

class BaseWidget(QWidget):
    """Foundation for all custom UI widgets"""
    
    data_changed = pyqtSignal()  # Signal when data changes
    
    def __init__(self, parent=None):
        super().__init__(parent)
        self.is_populated = False
        self.change_tracking_enabled = True
        
    def enable_change_tracking(self):
        """Enable change tracking and signals"""
        self.change_tracking_enabled = True
        
    def disable_change_tracking(self):
        """Disable change tracking (useful during population)"""
        self.change_tracking_enabled = False
        
    def emit_data_changed(self):
        """Emit data changed signal if tracking is enabled"""
        if self.change_tracking_enabled:
            self.data_changed.emit()


class BaseFormWidget(BaseWidget):
    """Base for form-style widgets with data collection/population"""
    
    def __init__(self, parent=None):
        super().__init__(parent)
        self.form_layout = QFormLayout()
        self.setLayout(self.form_layout)
        
    def add_field(self, label: str, widget: QWidget):
        """Add a field to the form"""
        self.form_layout.addRow(label, widget)
        
    def collect_data(self) -> Dict[str, Any]:
        """Collect data from form - override in subclasses"""
        return {}
        
    def populate_data(self, data: Dict[str, Any]):
        """Populate form with data - override in subclasses"""
        pass


class BaseTabWidget(BaseWidget):
    """Base for tab-based interfaces"""
    
    def __init__(self, parent=None):
        super().__init__(parent)
        self.tabs = {}  # Store tab references
        
    def add_tab(self, name: str, widget: QWidget):
        """Add a tab to the interface"""
        self.tabs[name] = widget


# ========== BASE EDITOR CLASS ==========

class BaseEditor(QWidget):
    """Base class for all Adventure Chess editors"""
    
    data_changed = pyqtSignal()
    
    def __init__(self, data_type: str, parent=None):
        super().__init__(parent)
        self.data_type = data_type  # 'piece' or 'ability'
        self.current_data = {}
        self.has_unsaved_changes = False
        
        # Don't initialize UI here - let subclass handle it
        
    def setup_ui(self):
        """Setup the editor UI - override in subclasses"""
        pass
        
    def collect_form_data(self) -> Dict[str, Any]:
        """Collect all data from the editor - override in subclasses"""
        return self.current_data.copy()
        
    def populate_form_data(self, data: Dict[str, Any]):
        """Populate editor with data - override in subclasses"""
        self.current_data = data.copy()
        
    def mark_changed(self):
        """Mark editor as having unsaved changes"""
        self.has_unsaved_changes = True
        self.data_changed.emit()
        
    def mark_saved(self):
        """Mark editor as saved"""
        self.has_unsaved_changes = False


# ========== BASE MANAGER CLASS ==========

class BaseManager(QObject):
    """Simplified base class for manager components"""
    
    def __init__(self, parent=None, manager_type: str = ""):
        super().__init__(parent)
        self.manager_type = manager_type
        self.errors = []
        
    def log_error(self, message: str, exception: Optional[Exception] = None):
        """Log an error"""
        error_msg = f"{self.manager_type}: {message}"
        if exception:
            error_msg += f" - {exception}"
        self.errors.append(error_msg)
        logger.error(error_msg)
        
    def get_errors(self) -> List[str]:
        """Get all errors"""
        return self.errors.copy()
        
    def clear_errors(self):
        """Clear error list"""
        self.errors.clear()


# ========== DATA HANDLER MIXIN ==========

class DataHandlerMixin:
    """Mixin for components that handle data operations"""
    
    def __init__(self):
        # Import here to avoid circular imports
        from data.unified_data_manager import data_manager
        self.data_manager = data_manager
        
    def save_data(self, data: Dict[str, Any], filename: Optional[str] = None) -> tuple[bool, Optional[str]]:
        """Save data using unified data manager"""
        if hasattr(self, 'data_type'):
            if self.data_type == 'piece':
                return self.data_manager.save_piece(data, filename)
            elif self.data_type == 'ability':
                return self.data_manager.save_ability(data, filename)
        return False, "Unknown data type"
        
    def load_data(self, filename: str) -> tuple[Optional[Dict], Optional[str]]:
        """Load data using unified data manager"""
        if hasattr(self, 'data_type'):
            if self.data_type == 'piece':
                return self.data_manager.load_piece(filename)
            elif self.data_type == 'ability':
                return self.data_manager.load_ability(filename)
        return None, "Unknown data type"
        
    def list_data(self) -> List[str]:
        """List available data files"""
        if hasattr(self, 'data_type'):
            if self.data_type == 'piece':
                return self.data_manager.list_pieces()
            elif self.data_type == 'ability':
                return self.data_manager.list_abilities()
        return []


# ========== VALIDATION MIXIN ==========

class ValidationMixin:
    """Mixin for components that need validation"""
    
    def __init__(self):
        self.validation_errors = []
        
    def validate(self, data: Dict[str, Any]) -> List[str]:
        """Validate data - override in subclasses"""
        return []
        
    def is_valid(self, data: Dict[str, Any]) -> bool:
        """Check if data is valid"""
        errors = self.validate(data)
        return len(errors) == 0
        
    def get_validation_errors(self) -> List[str]:
        """Get current validation errors"""
        return self.validation_errors.copy()


# ========== ERROR HANDLING ==========

class ErrorHandler:
    """Simplified error handling"""
    
    @staticmethod
    def show_error(message: str, title: str = "Error"):
        """Show error message to user"""
        from PyQt6.QtWidgets import QMessageBox
        QMessageBox.critical(None, title, message)
        
    @staticmethod
    def show_warning(message: str, title: str = "Warning"):
        """Show warning message to user"""
        from PyQt6.QtWidgets import QMessageBox
        QMessageBox.warning(None, title, message)
        
    @staticmethod
    def show_info(message: str, title: str = "Information"):
        """Show info message to user"""
        from PyQt6.QtWidgets import QMessageBox
        QMessageBox.information(None, title, message)


# ========== LAYOUT UTILITIES ==========

class LayoutUtils:
    """Simple layout utilities"""
    
    @staticmethod
    def create_vbox(margin: int = 5, spacing: int = 5) -> QVBoxLayout:
        """Create vertical box layout"""
        layout = QVBoxLayout()
        layout.setContentsMargins(margin, margin, margin, margin)
        layout.setSpacing(spacing)
        return layout
        
    @staticmethod
    def create_hbox(margin: int = 5, spacing: int = 5) -> QHBoxLayout:
        """Create horizontal box layout"""
        layout = QHBoxLayout()
        layout.setContentsMargins(margin, margin, margin, margin)
        layout.setSpacing(spacing)
        return layout
        
    @staticmethod
    def create_form() -> QFormLayout:
        """Create form layout"""
        layout = QFormLayout()
        layout.setContentsMargins(5, 5, 5, 5)
        layout.setSpacing(5)
        return layout


# Export all classes
__all__ = [
    'BaseWidget',
    'BaseFormWidget', 
    'BaseTabWidget',
    'BaseEditor',
    'BaseManager',
    'DataHandlerMixin',
    'ValidationMixin',
    'ErrorHandler',
    'LayoutUtils'
]
