"""
Enhanced Main Window Components for Adventure Chess Creator

This module provides enhanced UI components for the main window including:
- Status bar with real-time information
- Too<PERSON><PERSON> with quick actions
- Enhanced menu system
- Performance monitoring display
- User feedback integration
"""

from typing import Optional, Dict, Any
from PyQt6.QtWidgets import (
    QMainWindow, QWidget, QVBoxLayout, QHBoxLayout, QLabel, 
    QPushButton, QToolBar, QStatusBar, QProgressBar, QFrame,
    QTextEdit, QSplitter, QTabWidget, QGroupBox, QScrollArea
)
from PyQt6.QtCore import Qt, QTimer, pyqtSignal
from PyQt6.QtGui import QAction, QIcon, QFont
import logging

from .enhanced_feedback import FeedbackManager
from ..performance.performance_monitor import get_performance_monitor

logger = logging.getLogger(__name__)


class EnhancedStatusBar(QStatusBar):
    """Enhanced status bar with performance metrics and real-time updates"""
    
    def __init__(self, parent=None):
        super().__init__(parent)
        self.setup_status_bar()
        self.setup_update_timer()
    
    def setup_status_bar(self):
        """Setup the status bar components"""
        # Memory usage label
        self.memory_label = QLabel("Memory: --")
        self.memory_label.setStyleSheet("color: #666; font-size: 10px;")
        self.addPermanentWidget(self.memory_label)
        
        # Separator
        separator1 = QFrame()
        separator1.setFrameShape(QFrame.Shape.VLine)
        separator1.setStyleSheet("color: #ccc;")
        self.addPermanentWidget(separator1)
        
        # Cache performance
        self.cache_label = QLabel("Cache: --")
        self.cache_label.setStyleSheet("color: #666; font-size: 10px;")
        self.addPermanentWidget(self.cache_label)
        
        # Separator
        separator2 = QFrame()
        separator2.setFrameShape(QFrame.Shape.VLine)
        separator2.setStyleSheet("color: #ccc;")
        self.addPermanentWidget(separator2)
        
        # File count
        self.file_count_label = QLabel("Files: --")
        self.file_count_label.setStyleSheet("color: #666; font-size: 10px;")
        self.addPermanentWidget(self.file_count_label)
        
        # Progress bar (hidden by default)
        self.progress_bar = QProgressBar()
        self.progress_bar.setMaximumWidth(150)
        self.progress_bar.setVisible(False)
        self.addPermanentWidget(self.progress_bar)
    
    def setup_update_timer(self):
        """Setup timer for regular status updates"""
        self.update_timer = QTimer()
        self.update_timer.timeout.connect(self.update_status)
        self.update_timer.start(2000)  # Update every 2 seconds
    
    def update_status(self):
        """Update status bar information"""
        try:
            # Update memory usage
            monitor = get_performance_monitor()
            memory_summary = monitor.get_metric_summary("memory_rss_mb", 1)
            if memory_summary:
                memory_mb = memory_summary['latest']
                self.memory_label.setText(f"Memory: {memory_mb:.0f}MB")
                
                # Color code based on usage
                if memory_mb > 300:
                    self.memory_label.setStyleSheet("color: #e74c3c; font-size: 10px; font-weight: bold;")
                elif memory_mb > 200:
                    self.memory_label.setStyleSheet("color: #f39c12; font-size: 10px;")
                else:
                    self.memory_label.setStyleSheet("color: #27ae60; font-size: 10px;")
            
            # Update cache performance
            cache_summary = monitor.get_metric_summary("cache_hit_rate", 1)
            if cache_summary:
                hit_rate = cache_summary['latest']
                self.cache_label.setText(f"Cache: {hit_rate:.0%}")
                
                # Color code based on hit rate
                if hit_rate < 0.5:
                    self.cache_label.setStyleSheet("color: #e74c3c; font-size: 10px; font-weight: bold;")
                elif hit_rate < 0.7:
                    self.cache_label.setStyleSheet("color: #f39c12; font-size: 10px;")
                else:
                    self.cache_label.setStyleSheet("color: #27ae60; font-size: 10px;")
            
            # Update file count
            self.update_file_count()
            
        except Exception as e:
            logger.debug(f"Error updating status bar: {e}")
    
    def update_file_count(self):
        """Update file count display"""
        try:
            from data.unified_data_manager import unified_data_manager
            pieces = unified_data_manager.list_available_pieces()
            abilities = unified_data_manager.list_available_abilities()
            total_files = len(pieces) + len(abilities)
            self.file_count_label.setText(f"Files: {len(pieces)}P + {len(abilities)}A = {total_files}")
        except Exception as e:
            logger.debug(f"Error updating file count: {e}")
    
    def show_progress(self, message: str = "Processing..."):
        """Show progress bar with message"""
        self.showMessage(message)
        self.progress_bar.setVisible(True)
        self.progress_bar.setRange(0, 0)  # Indeterminate
    
    def update_progress(self, value: int, maximum: int = 100, message: str = ""):
        """Update progress bar"""
        self.progress_bar.setRange(0, maximum)
        self.progress_bar.setValue(value)
        if message:
            self.showMessage(message)
    
    def hide_progress(self):
        """Hide progress bar"""
        self.progress_bar.setVisible(False)
        self.clearMessage()


class EnhancedToolBar(QToolBar):
    """Enhanced toolbar with quick actions and visual feedback"""
    
    # Signals
    new_piece_requested = pyqtSignal()
    new_ability_requested = pyqtSignal()
    refresh_requested = pyqtSignal()
    performance_report_requested = pyqtSignal()
    
    def __init__(self, parent=None):
        super().__init__("Quick Actions", parent)
        self.setup_toolbar()
    
    def setup_toolbar(self):
        """Setup toolbar actions"""
        self.setToolButtonStyle(Qt.ToolButtonStyle.ToolButtonTextUnderIcon)
        self.setMovable(False)
        
        # New Piece action
        new_piece_action = QAction("📝", self)
        new_piece_action.setText("New Piece")
        new_piece_action.setToolTip("Create a new chess piece")
        new_piece_action.triggered.connect(self.new_piece_requested.emit)
        self.addAction(new_piece_action)
        
        # New Ability action
        new_ability_action = QAction("⚡", self)
        new_ability_action.setText("New Ability")
        new_ability_action.setToolTip("Create a new ability")
        new_ability_action.triggered.connect(self.new_ability_requested.emit)
        self.addAction(new_ability_action)
        
        self.addSeparator()\n        
        # Refresh action
        refresh_action = QAction("🔄", self)
        refresh_action.setText("Refresh")
        refresh_action.setToolTip("Refresh all data and UI components")
        refresh_action.triggered.connect(self.refresh_requested.emit)
        self.addAction(refresh_action)
        
        self.addSeparator()
        
        # Performance report action
        perf_action = QAction("📊", self)
        perf_action.setText("Performance")
        perf_action.setToolTip("Show performance report")
        perf_action.triggered.connect(self.performance_report_requested.emit)
        self.addAction(perf_action)


class PerformancePanel(QWidget):
    """Panel showing real-time performance metrics"""
    
    def __init__(self, parent=None):
        super().__init__(parent)
        self.setup_ui()
        self.setup_update_timer()
    
    def setup_ui(self):
        """Setup the performance panel UI"""
        layout = QVBoxLayout()
        layout.setContentsMargins(10, 10, 10, 10)
        
        # Title
        title = QLabel("Performance Monitor")
        title.setFont(QFont("Arial", 12, QFont.Weight.Bold))
        title.setStyleSheet("color: #2c3e50; margin-bottom: 10px;")
        layout.addWidget(title)
        
        # Metrics display
        self.metrics_text = QTextEdit()
        self.metrics_text.setMaximumHeight(200)
        self.metrics_text.setReadOnly(True)
        self.metrics_text.setStyleSheet("""
            QTextEdit {
                background-color: #f8f9fa;
                border: 1px solid #dee2e6;
                border-radius: 4px;
                font-family: 'Courier New', monospace;
                font-size: 10px;
                padding: 5px;
            }
        """)
        layout.addWidget(self.metrics_text)
        
        # Control buttons
        button_layout = QHBoxLayout()
        
        self.clear_btn = QPushButton("Clear")
        self.clear_btn.clicked.connect(self.clear_metrics)
        self.clear_btn.setStyleSheet("""
            QPushButton {
                background-color: #6c757d;
                color: white;
                border: none;
                padding: 5px 10px;
                border-radius: 3px;
                font-size: 10px;
            }
            QPushButton:hover {
                background-color: #5a6268;
            }
        """)
        button_layout.addWidget(self.clear_btn)
        
        self.export_btn = QPushButton("Export Report")
        self.export_btn.clicked.connect(self.export_report)
        self.export_btn.setStyleSheet("""
            QPushButton {
                background-color: #007bff;
                color: white;
                border: none;
                padding: 5px 10px;
                border-radius: 3px;
                font-size: 10px;
            }
            QPushButton:hover {
                background-color: #0056b3;
            }
        """)
        button_layout.addWidget(self.export_btn)
        
        button_layout.addStretch()
        layout.addLayout(button_layout)
        
        self.setLayout(layout)
    
    def setup_update_timer(self):
        """Setup timer for performance updates"""
        self.update_timer = QTimer()
        self.update_timer.timeout.connect(self.update_metrics)
        self.update_timer.start(5000)  # Update every 5 seconds
    
    def update_metrics(self):
        """Update performance metrics display"""
        try:
            monitor = get_performance_monitor()
            report = monitor.get_performance_report()
            
            # Append new report with timestamp
            import time
            timestamp = time.strftime("%H:%M:%S")
            self.metrics_text.append(f"\\n[{timestamp}] Performance Update:")
            self.metrics_text.append(report)
            self.metrics_text.append("-" * 40)
            
            # Keep only last 10 reports
            text = self.metrics_text.toPlainText()
            lines = text.split('\\n')
            if len(lines) > 200:  # Approximately 10 reports
                self.metrics_text.clear()
                self.metrics_text.append('\\n'.join(lines[-100:]))
            
        except Exception as e:
            logger.error(f"Error updating performance metrics: {e}")
    
    def clear_metrics(self):
        """Clear the metrics display"""
        self.metrics_text.clear()
    
    def export_report(self):
        """Export performance report to file"""
        try:
            from PyQt6.QtWidgets import QFileDialog
            filename, _ = QFileDialog.getSaveFileName(
                self, "Export Performance Report", 
                f"performance_report_{int(time.time())}.txt",
                "Text Files (*.txt)"
            )
            
            if filename:
                monitor = get_performance_monitor()
                report = monitor.get_performance_report()
                
                with open(filename, 'w') as f:
                    f.write(f"Adventure Chess Performance Report\\n")
                    f.write(f"Generated: {time.strftime('%Y-%m-%d %H:%M:%S')}\\n")
                    f.write("=" * 50 + "\\n\\n")
                    f.write(report)
                
                logger.info(f"Performance report exported to {filename}")
                
        except Exception as e:
            logger.error(f"Error exporting performance report: {e}")


class EnhancedMainWindowMixin:
    """Mixin class to enhance the main window with additional features"""
    
    def setup_enhanced_ui(self):
        """Setup enhanced UI components"""
        # Replace status bar
        if hasattr(self, 'statusBar'):
            self.setStatusBar(EnhancedStatusBar(self))
        
        # Add toolbar
        self.toolbar = EnhancedToolBar(self)
        self.addToolBar(Qt.ToolBarArea.TopToolBarArea, self.toolbar)
        
        # Connect toolbar signals
        self.toolbar.new_piece_requested.connect(self.on_new_piece_requested)
        self.toolbar.new_ability_requested.connect(self.on_new_ability_requested)
        self.toolbar.refresh_requested.connect(self.on_refresh_requested)
        self.toolbar.performance_report_requested.connect(self.show_performance_report)
        
        # Setup feedback manager
        self.feedback_manager = FeedbackManager(self)
        
        # Add performance panel (initially hidden)
        self.performance_panel = PerformancePanel(self)
        self.performance_panel.setVisible(False)
    
    def on_new_piece_requested(self):
        """Handle new piece request from toolbar"""
        try:
            self.show_editor()  # Switch to piece editor
            if hasattr(self, 'editor') and self.editor:
                self.editor.reset_form()
            self.feedback_manager.show_info("New piece editor opened")
        except Exception as e:
            self.feedback_manager.show_error("Error", f"Failed to open piece editor: {e}")
    
    def on_new_ability_requested(self):
        """Handle new ability request from toolbar"""
        try:
            self.show_ability_editor()  # Switch to ability editor
            if hasattr(self, 'ability_editor') and self.ability_editor:
                self.ability_editor.reset_form()
            self.feedback_manager.show_info("New ability editor opened")
        except Exception as e:
            self.feedback_manager.show_error("Error", f"Failed to open ability editor: {e}")
    
    def on_refresh_requested(self):
        """Handle refresh request from toolbar"""
        try:
            self.statusBar().show_progress("Refreshing data...")
            
            # Refresh current editor
            current_widget = self.stack.currentWidget()
            if hasattr(current_widget, 'refresh_all_data'):
                current_widget.refresh_all_data()
            
            self.statusBar().hide_progress()
            self.feedback_manager.show_success("Data refreshed successfully")
        except Exception as e:
            self.statusBar().hide_progress()
            self.feedback_manager.show_error("Refresh Error", f"Failed to refresh data: {e}")
    
    def show_performance_report(self):
        """Show/hide performance report panel"""
        if self.performance_panel.isVisible():
            self.performance_panel.setVisible(False)
        else:
            self.performance_panel.setVisible(True)
            self.performance_panel.raise_()
    
    def show_operation_progress(self, message: str):
        """Show progress for long operations"""
        self.statusBar().show_progress(message)
    
    def hide_operation_progress(self):
        """Hide operation progress"""
        self.statusBar().hide_progress()
    
    def show_success_message(self, message: str):
        """Show success message to user"""
        self.feedback_manager.show_success(message)
    
    def show_error_message(self, title: str, message: str, suggestions: Optional[list] = None):
        """Show error message with recovery suggestions"""
        self.feedback_manager.show_error(title, message, suggestions)
    
    def show_warning_message(self, message: str):
        """Show warning message to user"""
        self.feedback_manager.show_warning(message)


def enhance_main_window(main_window_class):
    """Decorator to enhance a main window class with additional features"""
    
    class EnhancedMainWindow(main_window_class, EnhancedMainWindowMixin):
        def __init__(self, *args, **kwargs):
            super().__init__(*args, **kwargs)
            self.setup_enhanced_ui()
    
    return EnhancedMainWindow