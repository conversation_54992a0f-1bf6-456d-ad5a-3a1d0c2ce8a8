"""
Simplified Ability Editor for Adventure Chess Creator

This demonstrates the simplified architecture for the ability editor:
- Single file instead of multiple modules
- Uses unified data manager
- Clear separation of concerns within the file
- Mixins for shared functionality
"""

import logging
from typing import Dict, Any, Optional, List
import importlib
import os

from PyQt6.QtWidgets import (
    QWidget, QVBoxLayout, QHBoxLayout, QFormLayout,
    QLineEdit, QTextEdit, QComboBox, QPushButton, 
    QSpinBox, QCheckBox, QLabel, QMessageBox, QFileDialog,
    QListWidget, QListWidgetItem, QScrollArea
)
from PyQt6.QtCore import pyqtSignal

from core.base import BaseEditor, DataHandlerMixin, ValidationMixin, ErrorHandler, LayoutUtils
try:
    from config import DEFAULT_ABILITY
except ImportError:
    default_ability = {}

logger = logging.getLogger(__name__)


class TagManager:
    """Manages tag selection and configuration for the Ability Editor."""

    def __init__(self, parent):
        self.parent = parent
        self.current_tags = []
        self.tag_configs = {}
        self.config_display_area = None  # Will be set later
        self.load_tag_configs()

    def set_config_display_area(self, config_display_area):
        """Set the configuration display area for tag configurations."""
        self.config_display_area = config_display_area

    def load_tag_configs(self):
        """Dynamically load tag configurations from the tag_configs folder."""
        tag_configs_path = os.path.join(os.path.dirname(__file__), 'tag_configs')
        if not os.path.exists(tag_configs_path):
            logger.error(f"Tag configurations folder not found: {tag_configs_path}")
            return
        for file in os.listdir(tag_configs_path):
            if file.endswith('_config.py') and file != 'base_tag_config.py':
                module_name = file[:-3]  # Remove .py extension
                module_path = f"editors.tag_configs.{module_name}"
                try:
                    module = importlib.import_module(module_path)
                    config_class = getattr(module, module_name.title().replace('_', ''))
                    self.tag_configs[module_name] = config_class(self.parent)
                except Exception as e:
                    logger.error(f"Failed to load tag config {module_name}: {e}")

    def create_tags_ui(self, layout):
        """Create the tags UI in the editor."""
        # Section title
        title = QLabel("Tags")
        title.setStyleSheet("font-weight: bold; font-size: 12px;")
        layout.addWidget(title)

        # Available tags dropdown
        self.available_tags_dropdown = QComboBox()
        self.available_tags_dropdown.addItems(self.tag_configs.keys())
        self.available_tags_dropdown.currentTextChanged.connect(self.display_tag_configuration)
        layout.addWidget(self.available_tags_dropdown)

        # Configuration display area
        self.config_display_area = QVBoxLayout()
        layout.addLayout(self.config_display_area)

    def display_tag_configuration(self, tag_name):
        """Display the configuration for the selected tag."""
        # Clear the current configuration display
        for i in reversed(range(self.config_display_area.count())):
            widget = self.config_display_area.itemAt(i).widget()
            if widget:
                widget.setParent(None)

        # Load and display the new configuration
        if tag_name in self.tag_configs:
            tag_config = self.tag_configs[tag_name]
            tag_config.create_ui(self.config_display_area)
        else:
            logger.warning(f"No configuration found for tag: {tag_name}")

    def add_tag(self):
        """Add the selected tag and display its configuration."""
        selected_tag = self.available_tags_dropdown.currentText()
        if selected_tag and selected_tag not in self.current_tags:
            self.current_tags.append(selected_tag)
            self.display_tag_configuration(selected_tag)

    def remove_tag(self):
        """Remove the currently displayed tag configuration."""
        selected_tag = self.available_tags_dropdown.currentText()
        if selected_tag in self.current_tags:
            self.current_tags.remove(selected_tag)
            # Clear the configuration display area
            for i in reversed(range(self.config_display_area.count())):
                widget = self.config_display_area.itemAt(i).widget()
                if widget:
                    widget.setParent(None)


class AbilityEditor(BaseEditor, DataHandlerMixin, ValidationMixin):
    """
    Simplified ability editor using unified architecture.
    
    All functionality in one file with clear sections:
    1. UI Setup
    2. Data Operations  
    3. Event Handlers
    4. Validation
    5. Tag Management
    """
    
    def __init__(self, parent=None):
        super().__init__("ability", parent)
        self.tag_manager = TagManager(self)
        
        # Current state
        self.current_filename = None
        self.current_tags = []
        
        # Setup UI
        self.setup_ui()
        self.connect_signals()
        
        # Load default data
        self.load_default_data()

    # ========== UI SETUP ==========
    
    def setup_ui(self):
        """Setup the complete UI"""
        main_layout = LayoutUtils.create_vbox()
        self.setLayout(main_layout)
        
        # Add toolbar
        toolbar_layout = self.create_toolbar()
        main_layout.addLayout(toolbar_layout)
        
        # Add main content
        content_layout = self.create_content_area()
        main_layout.addLayout(content_layout)
        
        # Add status area
        status_layout = self.create_status_area()
        main_layout.addLayout(status_layout)
    
    def create_toolbar(self) -> QHBoxLayout:
        """Create toolbar with file operations"""
        layout = LayoutUtils.create_hbox()
        
        self.new_btn = QPushButton("New")
        self.load_btn = QPushButton("Load")
        self.save_btn = QPushButton("Save") 
        self.save_as_btn = QPushButton("Save As")
        
        layout.addWidget(self.new_btn)
        layout.addWidget(self.load_btn)
        layout.addWidget(self.save_btn)
        layout.addWidget(self.save_as_btn)
        layout.addStretch()  # Push buttons to left
        
        return layout
    
    def create_content_area(self) -> QVBoxLayout:
        """Create main content area"""
        layout = LayoutUtils.create_vbox()
        
        # Basic info section
        basic_section = self.create_basic_info_section()
        layout.addWidget(basic_section)
        
        # Tags section  
        tags_section = self.create_tags_section()
        layout.addWidget(tags_section)
        
        # Configuration section
        config_section = self.create_configuration_section()
        layout.addWidget(config_section)
        
        return layout
    
    def create_basic_info_section(self) -> QWidget:
        """Create basic ability info section"""
        widget = QWidget()
        layout = LayoutUtils.create_form()
        
        # Name field
        self.name_input = QLineEdit()
        layout.addRow("Name:", self.name_input)
        
        # Description field
        self.description_input = QTextEdit()
        self.description_input.setMaximumHeight(100)
        layout.addRow("Description:", self.description_input)
        
        # Cost field
        self.cost_input = QSpinBox()
        self.cost_input.setRange(0, 20)
        self.cost_input.setValue(1)
        layout.addRow("Cost:", self.cost_input)
        
        # Recharge type
        self.recharge_input = QComboBox()
        self.recharge_input.addItems(['turnRecharge', 'consumable', 'passive'])
        layout.addRow("Recharge Type:", self.recharge_input)
        
        widget.setLayout(layout)
        return widget
    
    def create_tags_section(self) -> QWidget:
        """Create tags configuration section with dynamic editor loading"""
        widget = QWidget()
        layout = LayoutUtils.create_vbox()

        # Section title
        title = QLabel("Tags")
        title.setStyleSheet("font-weight: bold; font-size: 12px;")
        layout.addWidget(title)

       # Available tags dropdown
        self.tag_manager.available_tags_dropdown = QComboBox()
        for tag_name, config_instance in self.tag_manager.tag_configs.items():
            if hasattr(config_instance, 'get_title'):
                self.tag_manager.available_tags_dropdown.addItem(config_instance.get_title())
            else:
                self.tag_manager.available_tags_dropdown.addItem(tag_name)
        self.tag_manager.available_tags_dropdown.currentTextChanged.connect(self.tag_manager.add_tag)
        layout.addWidget(self.tag_manager.available_tags_dropdown)

        # Tags list
        self.tag_manager.tags_list = QListWidget()
        self.tag_manager.tags_list.setMaximumHeight(150)
        layout.addWidget(self.tag_manager.tags_list)

        # Tag management buttons
        tag_buttons_layout = QHBoxLayout()
        self.tag_manager.remove_tag_btn = QPushButton("Remove Tag")

        tag_buttons_layout.addWidget(self.tag_manager.remove_tag_btn)
        tag_buttons_layout.addStretch()

        layout.addLayout(tag_buttons_layout)

        # Connect buttons to actions
        self.tag_manager.remove_tag_btn.clicked.connect(self.tag_manager.remove_tag)

        widget.setLayout(layout)
        return widget
    
    def create_configuration_section(self) -> QWidget:
        """Create configuration section"""
        widget = QWidget()
        layout = LayoutUtils.create_vbox()
        
        # Section title
        title = QLabel("Configuration")
        title.setStyleSheet("font-weight: bold; font-size: 12px;")
        layout.addWidget(title)
        
        # Scroll area for configuration options
        scroll = QScrollArea()
        scroll.setWidgetResizable(True)
        scroll.setMaximumHeight(200)
        
        self.config_widget = QWidget()
        self.config_layout = LayoutUtils.create_form()
        self.config_widget.setLayout(self.config_layout)
        
        scroll.setWidget(self.config_widget)
        layout.addWidget(scroll)

        # Set the config layout as the display area for tag manager
        self.tag_manager.set_config_display_area(self.config_layout)

        widget.setLayout(layout)
        return widget
    
    def create_status_area(self) -> QHBoxLayout:
        """Create status area"""
        layout = LayoutUtils.create_hbox()
        
        self.status_label = QLabel("Ready")
        layout.addWidget(self.status_label)
        layout.addStretch()
        
        return layout

    # ========== DATA OPERATIONS ==========
    
    def collect_form_data(self) -> Dict[str, Any]:
        """Collect all data from the form"""
        data = {
            'version': '1.0.0',
            'name': self.name_input.text(),
            'description': self.description_input.toPlainText(),
            'cost': self.cost_input.value(),
            'recharge_type': self.recharge_input.currentText(),
            'tags': self.current_tags.copy()
        }
        
        return data
    
    def populate_form_data(self, data: Dict[str, Any]):
        """Populate form with data"""
        self.disable_change_tracking()
        
        try:
            # Basic info
            self.name_input.setText(data.get('name', ''))
            self.description_input.setPlainText(data.get('description', ''))
            self.cost_input.setValue(data.get('cost', 1))
            
            # Recharge type
            recharge_type = data.get('recharge_type', 'turnRecharge')
            index = self.recharge_input.findText(recharge_type)
            if index >= 0:
                self.recharge_input.setCurrentIndex(index)
            
            # Tags
            self.current_tags = data.get('tags', [])
            self.update_tags_display()
            
            # Update status
            self.status_label.setText(f"Loaded: {data.get('name', 'Unnamed')}")
            self.mark_saved()
            
        finally:
            self.enable_change_tracking()
    
    def load_default_data(self):
        """Load default ability data"""
        try:
            from config import DEFAULT_ABILITY
            default_data = DEFAULT_ABILITY
        except ImportError:
            # Fallback default
            default_data = {
                'name': '',
                'description': '',
                'cost': 1,
                'recharge_type': 'turnRecharge',
                'tags': []
            }
        self.populate_form_data(default_data)

    # ========== TAG MANAGEMENT ==========
    
    def update_tags_display(self):
        """Update the tags list display"""
        self.tag_manager.tags_list.clear()
        for tag in self.current_tags:
            if isinstance(tag, dict):
                tag_name = tag.get('name', 'Unknown Tag')
                item_text = f"{tag_name}"
            else:
                item_text = str(tag)
            
            item = QListWidgetItem(item_text)
            self.tag_manager.tags_list.addItem(item)
    
    # ========== EVENT HANDLERS ==========
    
    def connect_signals(self):
        """Connect UI signals to handlers"""
        # File operations
        self.new_btn.clicked.connect(self.handle_new)
        self.load_btn.clicked.connect(self.handle_load)
        self.save_btn.clicked.connect(self.handle_save)
        self.save_as_btn.clicked.connect(self.handle_save_as)
        
        # Change tracking
        self.name_input.textChanged.connect(self.mark_changed)
        self.description_input.textChanged.connect(self.mark_changed)
        self.cost_input.valueChanged.connect(self.mark_changed)
        self.recharge_input.currentTextChanged.connect(self.mark_changed)
    
    def handle_new(self):
        """Handle new ability creation"""
        if self.has_unsaved_changes:
            reply = QMessageBox.question(
                self, "Unsaved Changes",
                "You have unsaved changes. Continue?",
                QMessageBox.StandardButton.Yes | QMessageBox.StandardButton.No
            )
            if reply == QMessageBox.StandardButton.No:
                return
        
        self.current_filename = None
        self.load_default_data()
        self.status_label.setText("New ability created")
    
    def handle_load(self):
        """Handle ability loading"""
        abilities = self.list_data()
        if not abilities:
            ErrorHandler.show_info("No abilities found")
            return
        
        # Simple file selection (in real implementation, use a proper dialog)
        filename, ok = QFileDialog.getOpenFileName(
            self, "Load Ability", "", "Ability Files (*.json)"
        )
        
        if ok and filename:
            # Extract just the filename without path/extension
            import os
            base_name = os.path.splitext(os.path.basename(filename))[0]
            
            data, error = self.load_data(base_name)
            if error:
                ErrorHandler.show_error(f"Failed to load ability: {error}")
                return
            
            self.current_filename = base_name
            self.populate_form_data(data)
    
    def handle_save(self):
        """Handle ability saving"""
        if not self.current_filename:
            self.handle_save_as()
            return
        
        data = self.collect_form_data()
        
        # Validate data
        errors = self.validate(data)
        if errors:
            ErrorHandler.show_error(f"Validation errors:\n" + "\n".join(errors))
            return
        
        success, error = self.save_data(data, self.current_filename)
        if success:
            self.mark_saved()
            self.status_label.setText(f"Saved: {self.current_filename}")
        else:
            ErrorHandler.show_error(f"Failed to save: {error}")
    
    def handle_save_as(self):
        """Handle save as operation"""
        data = self.collect_form_data()
        
        # Get filename from name field or ask user
        default_name = data.get('name', 'unnamed_ability')
        filename, ok = QFileDialog.getSaveFileName(
            self, "Save Ability As", default_name, "Ability Files (*.json)"
        )
        
        if ok and filename:
            # Extract just the filename without path/extension
            import os
            base_name = os.path.splitext(os.path.basename(filename))[0]
            
            success, error = self.save_data(data, base_name)
            if success:
                self.current_filename = base_name
                self.mark_saved()
                self.status_label.setText(f"Saved as: {base_name}")
            else:
                ErrorHandler.show_error(f"Failed to save: {error}")

    # ========== VALIDATION ==========
    
    def validate(self, data: Dict[str, Any]) -> List[str]:
        """Validate ability data"""
        errors = []
        
        # Name is required
        if not data.get('name', '').strip():
            errors.append("Ability name is required")
        
        # Cost validation
        cost = data.get('cost', 0)
        if cost < 0:
            errors.append("Cost cannot be negative")
        
        # Tags validation
        tags = data.get('tags', [])
        if len(tags) == 0:
            errors.append("At least one tag is required")
        
        return errors

    # ========== UTILITY METHODS ==========
    
    def enable_change_tracking(self):
        """Enable change tracking"""
        self.change_tracking_enabled = True
        
    def disable_change_tracking(self):
        """Disable change tracking"""
        self.change_tracking_enabled = False
        
    def mark_changed(self):
        """Mark as changed and emit signal"""
        if hasattr(self, 'change_tracking_enabled') and self.change_tracking_enabled:
            self.has_unsaved_changes = True
            self.data_changed.emit()
            
            # Update status
            status_text = "Modified"
            if self.current_filename:
                status_text += f": {self.current_filename}"
            self.status_label.setText(status_text)
