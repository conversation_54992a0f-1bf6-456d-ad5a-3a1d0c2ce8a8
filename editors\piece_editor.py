"""
Simplified Piece Editor for Adventure Chess Creator

This demonstrates the simplified architecture:
- Single file instead of multiple modules
- Uses unified data manager
- Clear separation of concerns within the file
- Mixins for shared functionality
"""

import logging
from typing import Dict, Any, Optional, List

from PyQt6.QtWidgets import (
    QWidget, Q<PERSON>oxLayout, QHB<PERSON>Layout, QFormLayout,
    QLineEdit, QTextEdit, QComboBox, QPushButton, 
    QSpinBox, QCheckBox, QLabel, QMessageBox, QFileDialog
)
from PyQt6.QtCore import pyqtSignal

from core.base import BaseEditor, DataHandlerMixin, ValidationMixin, ErrorHandler, LayoutUtils
try:
    from config import DEFAULT_PIECE
    default_piece_data = DEFAULT_PIECE
except ImportError:
    default_piece_data = {}

logger = logging.getLogger(__name__)


class PieceEditor(BaseEditor, DataHandlerMixin, ValidationMixin):
    """
    Simplified piece editor using unified architecture.
    
    All functionality in one file with clear sections:
    1. UI Setup
    2. Data Operations  
    3. Event Handlers
    4. Validation
    """
    
    def __init__(self, parent=None):
        BaseEditor.__init__(self, "piece", parent)
        DataHandlerMixin.__init__(self)
        ValidationMixin.__init__(self)
        
        # Current state
        self.current_filename = None
        self.current_movement_data = {}
        self.current_abilities = []
        self.current_recharge_config = {
            'max_points': 0,
            'starting_points': 0,
            'recharge_type': 'none',
            'turn_points': 1,
            'adjacency_config': None,
            'commitment_turns': 1
        }
        
        # Setup UI
        self.setup_ui()
        self.connect_signals()
        
        # Load default data
        self.load_default_data()

        # Initialize UI state
        self.initialize_ui_state()

    # ========== UI SETUP ==========
    
    def setup_ui(self):
        """Setup the complete UI"""
        main_layout = LayoutUtils.create_vbox()
        self.setLayout(main_layout)
        
        # Add toolbar
        toolbar_layout = self.create_toolbar()
        main_layout.addLayout(toolbar_layout)
        
        # Add main content
        content_layout = self.create_content_area()
        main_layout.addLayout(content_layout)
        
        # Add status area
        status_layout = self.create_status_area()
        main_layout.addLayout(status_layout)
    
    def create_toolbar(self) -> QHBoxLayout:
        """Create toolbar with file operations"""
        layout = LayoutUtils.create_hbox()
        
        self.new_btn = QPushButton("New")
        self.load_btn = QPushButton("Load")
        self.save_btn = QPushButton("Save") 
        self.save_as_btn = QPushButton("Save As")
        
        layout.addWidget(self.new_btn)
        layout.addWidget(self.load_btn)
        layout.addWidget(self.save_btn)
        layout.addWidget(self.save_as_btn)
        layout.addStretch()  # Push buttons to left
        
        return layout
    
    def create_content_area(self) -> QVBoxLayout:
        """Create main content area"""
        layout = LayoutUtils.create_vbox()
        
        # Basic info section
        basic_section = self.create_basic_info_section()
        layout.addWidget(basic_section)
        
        # Movement section  
        movement_section = self.create_movement_section()
        layout.addWidget(movement_section)
        
        # Abilities section
        abilities_section = self.create_abilities_section()
        layout.addWidget(abilities_section)
        
        return layout
    
    def create_basic_info_section(self) -> QWidget:
        """Create basic piece info section"""
        widget = QWidget()
        layout = LayoutUtils.create_form()
        
        # Name field
        self.name_input = QLineEdit()
        layout.addRow("Name:", self.name_input)
        
        # Description field
        self.description_input = QTextEdit()
        self.description_input.setMaximumHeight(100)
        layout.addRow("Description:", self.description_input)
        
        # Role field
        self.role_input = QComboBox()
        self.role_input.addItems(['Commander', 'Support'])
        layout.addRow("Role:", self.role_input)
        
        # Capture ability
        self.can_capture_input = QCheckBox("Can capture other pieces")
        self.can_capture_input.setChecked(True)
        layout.addRow("", self.can_capture_input)
        
        widget.setLayout(layout)
        return widget
    
    def create_movement_section(self) -> QWidget:
        """Create movement configuration section"""
        widget = QWidget()
        layout = LayoutUtils.create_vbox()
        
        # Section title
        title = QLabel("Movement Configuration")
        title.setStyleSheet("font-weight: bold; font-size: 12px;")
        layout.addWidget(title)
        
        # Movement type selector
        type_layout = LayoutUtils.create_hbox()
        type_layout.addWidget(QLabel("Movement Type:"))
        
        self.movement_type = QComboBox()
        # Add items with display names but store actual values
        movement_types = [
            ('Rook (Orthogonal)', 'orthogonal'),
            ('Bishop (Diagonal)', 'diagonal'),
            ('Knight (L-Shape)', 'lShape'),
            ('Queen (Any Direction)', 'any'),
            ('Custom', 'custom')
        ]
        for display_name, value in movement_types:
            self.movement_type.addItem(display_name, value)
        type_layout.addWidget(self.movement_type)
        type_layout.addStretch()
        
        layout.addLayout(type_layout)
        
        # Pattern editor button
        self.pattern_btn = QPushButton("Edit Movement Pattern")
        layout.addWidget(self.pattern_btn)
        
        widget.setLayout(layout)
        return widget
    
    def create_abilities_section(self) -> QWidget:
        """Create abilities section"""
        widget = QWidget()
        layout = LayoutUtils.create_vbox()
        
        # Section title
        title = QLabel("Abilities")
        title.setStyleSheet("font-weight: bold; font-size: 12px;")
        layout.addWidget(title)
        
        # Recharge configuration
        self.recharge_btn = QPushButton("Configure Recharge & Points")
        layout.addWidget(self.recharge_btn)

        # Abilities list (simplified)
        self.abilities_btn = QPushButton("Manage Abilities")
        layout.addWidget(self.abilities_btn)
        
        widget.setLayout(layout)
        return widget
    
    def create_status_area(self) -> QHBoxLayout:
        """Create status area"""
        layout = LayoutUtils.create_hbox()
        
        self.status_label = QLabel("Ready")
        layout.addWidget(self.status_label)
        layout.addStretch()
        
        return layout

    # ========== DATA OPERATIONS ==========
    
    def collect_form_data(self) -> Dict[str, Any]:
        """Collect all data from the form"""
        data = {
            'version': '1.0.0',
            'name': self.name_input.text(),
            'description': self.description_input.toPlainText(),
            'role': self.role_input.currentText(),
            'can_capture': self.can_capture_input.isChecked(),
            'max_points': self.current_recharge_config.get('max_points', 0),
            'starting_points': self.current_recharge_config.get('starting_points', 0),
            'movement': self.current_movement_data or {
                'type': self.movement_type.currentData() or 'orthogonal',
                'pattern': None,
                'piece_position': [3, 3]
            },
            'abilities': self.current_abilities,
            'recharge_type': self.current_recharge_config.get('recharge_type', 'none'),
            'turn_points': self.current_recharge_config.get('turn_points', 1),
            'adjacency_config': self.current_recharge_config.get('adjacency_config'),
            'commitment_turns': self.current_recharge_config.get('commitment_turns', 1)
        }
        
        return data
    
    def populate_form_data(self, data: Dict[str, Any]):
        """Populate form with data"""
        self.disable_change_tracking()
        
        try:
            # Basic info
            self.name_input.setText(data.get('name', ''))
            self.description_input.setPlainText(data.get('description', ''))
            
            # Role
            role = data.get('role', 'Commander')
            index = self.role_input.findText(role)
            if index >= 0:
                self.role_input.setCurrentIndex(index)
            
            # Capture ability
            self.can_capture_input.setChecked(data.get('can_capture', True))

            # Recharge configuration
            self.current_recharge_config.update({
                'max_points': data.get('max_points', 0),
                'starting_points': data.get('starting_points', 0),
                'recharge_type': data.get('recharge_type', 'none'),
                'turn_points': data.get('turn_points', 1),
                'adjacency_config': data.get('adjacency_config'),
                'commitment_turns': data.get('commitment_turns', 1)
            })
            
            # Movement
            movement = data.get('movement', {})
            if movement:
                movement_type = movement.get('type', 'orthogonal')
                # Find index by data value, not display text
                index = -1
                for i in range(self.movement_type.count()):
                    if self.movement_type.itemData(i) == movement_type:
                        index = i
                        break
                if index >= 0:
                    self.movement_type.setCurrentIndex(index)

                self.current_movement_data = movement

            # Abilities
            self.current_abilities = data.get('abilities', [])

            # Update status
            self.status_label.setText(f"Loaded: {data.get('name', 'Unnamed')}")
            self.mark_saved()
            
        finally:
            self.enable_change_tracking()
    
    def load_default_data(self):
        """Load default piece data"""
        self.populate_form_data(default_piece_data)

    def initialize_ui_state(self):
        """Initialize UI state after setup"""
        # Set initial pattern button state based on movement type
        current_type = self.movement_type.currentData()
        self.pattern_btn.setEnabled(current_type == 'custom')

    # ========== EVENT HANDLERS ==========
    
    def connect_signals(self):
        """Connect UI signals to handlers"""
        # File operations
        self.new_btn.clicked.connect(self.handle_new)
        self.load_btn.clicked.connect(self.handle_load)
        self.save_btn.clicked.connect(self.handle_save)
        self.save_as_btn.clicked.connect(self.handle_save_as)
        
        # Pattern editor
        self.pattern_btn.clicked.connect(self.handle_edit_pattern)
        
        # Recharge configuration
        self.recharge_btn.clicked.connect(self.handle_edit_recharge)

        # Abilities
        self.abilities_btn.clicked.connect(self.handle_edit_abilities)
        
        # Movement type change handler
        self.movement_type.currentIndexChanged.connect(self.on_movement_type_changed)

        # Change tracking
        self.name_input.textChanged.connect(self.mark_changed)
        self.description_input.textChanged.connect(self.mark_changed)
        self.role_input.currentTextChanged.connect(self.mark_changed)
        self.can_capture_input.toggled.connect(self.mark_changed)
        self.movement_type.currentIndexChanged.connect(self.mark_changed)
    
    def handle_new(self):
        """Handle new piece creation"""
        if self.has_unsaved_changes:
            reply = QMessageBox.question(
                self, "Unsaved Changes",
                "You have unsaved changes. Continue?",
                QMessageBox.StandardButton.Yes | QMessageBox.StandardButton.No
            )
            if reply == QMessageBox.StandardButton.No:
                return
        
        self.current_filename = None
        self.load_default_data()
        self.status_label.setText("New piece created")
    
    def handle_load(self):
        """Handle piece loading"""
        pieces = self.list_data()
        if not pieces:
            ErrorHandler.show_info("No pieces found")
            return
        
        # Simple file selection (in real implementation, use a proper dialog)
        filename, ok = QFileDialog.getOpenFileName(
            self, "Load Piece", "", "Piece Files (*.json)"
        )
        
        if ok and filename:
            # Extract just the filename without path/extension
            import os
            base_name = os.path.splitext(os.path.basename(filename))[0]
            
            data, error = self.load_data(base_name)
            if error:
                ErrorHandler.show_error(f"Failed to load piece: {error}")
                return
            
            self.current_filename = base_name
            self.populate_form_data(data)
    
    def handle_save(self):
        """Handle piece saving"""
        if not self.current_filename:
            self.handle_save_as()
            return
        
        data = self.collect_form_data()
        
        # Validate data
        errors = self.validate(data)
        if errors:
            ErrorHandler.show_error(f"Validation errors:\n" + "\n".join(errors))
            return
        
        success, error = self.save_data(data, self.current_filename)
        if success:
            self.mark_saved()
            self.status_label.setText(f"Saved: {self.current_filename}")
        else:
            ErrorHandler.show_error(f"Failed to save: {error}")
    
    def handle_save_as(self):
        """Handle save as operation"""
        data = self.collect_form_data()
        
        # Get filename from name field or ask user
        default_name = data.get('name', 'unnamed_piece')
        filename, ok = QFileDialog.getSaveFileName(
            self, "Save Piece As", default_name, "Piece Files (*.json)"
        )
        
        if ok and filename:
            # Extract just the filename without path/extension
            import os
            base_name = os.path.splitext(os.path.basename(filename))[0]
            
            success, error = self.save_data(data, base_name)
            if success:
                self.current_filename = base_name
                self.mark_saved()
                self.status_label.setText(f"Saved as: {base_name}")
            else:
                ErrorHandler.show_error(f"Failed to save: {error}")
    
    def on_movement_type_changed(self):
        """Handle movement type selection change"""
        current_type = self.movement_type.currentData()
        # Enable pattern button only for custom movement
        self.pattern_btn.setEnabled(current_type == 'custom')
        self.mark_changed()

    def handle_edit_pattern(self):
        """Handle movement pattern editing"""
        from dialogs.pattern_editor_dialog import edit_single_pattern

        # Get current pattern data
        current_pattern = None
        checkbox_states = None

        if self.current_movement_data:
            current_pattern = self.current_movement_data.get('pattern')
            # Default checkbox states if not present
            checkbox_states = {
                'starting_square_checked': False,
                'continue_off_board_checked': False
            }

        # Open pattern editor
        pattern, new_checkbox_states = edit_single_pattern(
            initial_pattern=current_pattern,
            title="Edit Movement Pattern",
            parent=self,
            checkbox_states=checkbox_states
        )

        if pattern is not None:
            # Update movement data
            if not self.current_movement_data:
                self.current_movement_data = {}

            self.current_movement_data.update({
                'type': 'custom',
                'pattern': pattern,
                'piece_position': [3, 3]  # Default piece position
            })

            self.mark_changed()

    def handle_edit_recharge(self):
        """Handle recharge configuration editing"""
        from dialogs.recharge_config_dialog import edit_recharge_config

        # Open recharge configuration dialog
        config = edit_recharge_config(
            parent=self,
            initial_config=self.current_recharge_config.copy()
        )

        if config is not None:
            self.current_recharge_config.update(config)
            self.mark_changed()

    def handle_edit_abilities(self):
        """Handle abilities editing"""
        from dialogs.piece_ability_manager import PieceAbilityManagerDialog

        # Open abilities manager dialog
        dialog = PieceAbilityManagerDialog(
            parent=self,
            piece_abilities=self.current_abilities.copy()
        )

        if dialog.exec() == dialog.DialogCode.Accepted:
            # Get updated abilities from dialog
            data = dialog.collect_data()
            self.current_abilities = data.get('piece_abilities', [])
            self.mark_changed()

    # ========== VALIDATION ==========
    
    def validate(self, data: Dict[str, Any]) -> List[str]:
        """Validate piece data"""
        errors = []
        
        # Name is required
        if not data.get('name', '').strip():
            errors.append("Piece name is required")
        
        # Points validation
        max_points = data.get('max_points', 0)
        starting_points = data.get('starting_points', 0)
        
        if starting_points > max_points:
            errors.append("Starting points cannot exceed max points")
        
        # Movement validation
        movement = data.get('movement', {})
        if not movement.get('type'):
            errors.append("Movement type is required")
        
        return errors

    # ========== UTILITY METHODS ==========
    
    def enable_change_tracking(self):
        """Enable change tracking"""
        self.change_tracking_enabled = True
        
    def disable_change_tracking(self):
        """Disable change tracking"""
        self.change_tracking_enabled = False
        
    def mark_changed(self):
        """Mark as changed and emit signal"""
        if hasattr(self, 'change_tracking_enabled') and self.change_tracking_enabled:
            self.has_unsaved_changes = True
            self.data_changed.emit()
            
            # Update status
            status_text = "Modified"
            if self.current_filename:
                status_text += f": {self.current_filename}"
            self.status_label.setText(status_text)


# For backward compatibility
PieceEditorWindow = PieceEditor
