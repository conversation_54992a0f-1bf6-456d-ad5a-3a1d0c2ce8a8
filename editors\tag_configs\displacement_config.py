"""
Displacement tag configuration for ability editor.
Handles displacement-based ability configurations with custom patterns.
"""

from PyQt6.QtWidgets import (QFormLayout, QComboBox, QSpinBox, QCheckBox,
                            QPushButton, QLabel, QWidget, QVBoxLayout)
from typing import Dict, Any
from .base_tag_config import BaseTagConfig
from core.ui import InlinePieceSelector


class DisplacementConfig(BaseTagConfig):
    """Configuration for displacement tag abilities."""
    
    def __init__(self, editor):
        super().__init__(editor, "displacePiece")
        # Initialize displacement pattern
        self.displacement_custom_map = None
        self.displacement_piece_position = [3, 3]
    
    def create_ui(self, parent_layout) -> None:
        """
        Create the UI widgets for displacement configuration.
        
        Args:
            parent_layout: The layout to add widgets to
        """
        try:
            self.log_debug("Starting displacement UI creation")
            
            # Create main widget
            main_widget = QWidget()
            form_layout = QFormLayout()
            
            # Displacement direction
            self.displacement_direction_combo = QComboBox()
            self.displacement_direction_combo.addItems([
                "North", "South", "East", "West", 
                "Northeast", "Northwest", "Southeast", "Southwest",
                "Custom"
            ])
            self.displacement_direction_combo.currentTextChanged.connect(self.on_displacement_direction_changed)
            form_layout.addRow("Direction:", self.displacement_direction_combo)
            self.store_widget("displacement_direction", self.displacement_direction_combo)
            
            # Displacement distance
            self.displacement_distance_spin = QSpinBox()
            self.displacement_distance_spin.setRange(1, 8)
            self.displacement_distance_spin.setValue(1)
            self.displacement_distance_spin.setSuffix(" squares")
            form_layout.addRow("Distance:", self.displacement_distance_spin)
            self.store_widget("displacement_distance", self.displacement_distance_spin)

            # Target pieces selector (matching old editor exactly)
            displacement_target_selector = InlinePieceSelector(
                parent=self.editor,
                title="Target Pieces",
                allow_costs=True
            )
            form_layout.addRow("Target Pieces:", displacement_target_selector)
            self.store_widget("displacement_target_selector", displacement_target_selector)
            self.log_debug("Added displacement target selector")

            # Force displacement
            self.displacement_force_check = QCheckBox("Force displacement (ignore obstacles)")
            form_layout.addRow("Force:", self.displacement_force_check)
            self.store_widget("displacement_force", self.displacement_force_check)
            
            # Push through pieces
            self.displacement_push_check = QCheckBox("Push through other pieces")
            form_layout.addRow("Push Through:", self.displacement_push_check)
            self.store_widget("displacement_push", self.displacement_push_check)
            
            # Damage on collision
            self.displacement_damage_check = QCheckBox("Deal damage on collision")
            form_layout.addRow("Collision Damage:", self.displacement_damage_check)
            self.store_widget("displacement_damage", self.displacement_damage_check)
            
            # Custom pattern section
            pattern_widget = QWidget()
            pattern_layout = QVBoxLayout()
            
            pattern_info = QLabel("Define custom displacement pattern:")
            pattern_info.setStyleSheet("color: #666; font-style: italic;")
            pattern_layout.addWidget(pattern_info)
            
            self.displacement_custom_btn = QPushButton("Edit Custom Displacement Pattern")
            self.displacement_custom_btn.setStyleSheet("""
                QPushButton {
                    background-color: #FF9800;
                    color: white;
                    font-weight: bold;
                    padding: 8px 16px;
                    border: none;
                    border-radius: 4px;
                }
                QPushButton:hover {
                    background-color: #F57C00;
                }
            """)
            self.displacement_custom_btn.clicked.connect(self.edit_displacement_pattern)
            self.displacement_custom_btn.setVisible(False)  # Initially hidden
            pattern_layout.addWidget(self.displacement_custom_btn)
            
            pattern_widget.setLayout(pattern_layout)
            form_layout.addRow("Custom Pattern:", pattern_widget)
            self.store_widget("displacement_custom_btn", self.displacement_custom_btn)
            
            main_widget.setLayout(form_layout)
            parent_layout.addWidget(main_widget)
            
            # Connect change signals
            self.connect_change_signals()
            
            self.log_debug("Displacement UI creation completed")
            
        except Exception as e:
            self.log_error(f"Error creating UI: {e}")
    
    def on_displacement_direction_changed(self, direction):
        """Handle displacement direction change."""
        try:
            custom_btn = self.get_widget_by_name("displacement_custom_btn")
            if custom_btn:
                custom_btn.setVisible(direction == "Custom")
            
            # Mark as changed
            if hasattr(self.editor, 'mark_unsaved_changes'):
                self.editor.mark_unsaved_changes()
                
        except Exception as e:
            self.log_error(f"Error handling direction change: {e}")
    
    def edit_displacement_pattern(self):
        """Edit custom displacement pattern using range editor dialog."""
        try:
            from dialogs.range_editor_dialog import edit_range_pattern
            
            # Get initial pattern if exists
            initial_pattern = getattr(self, 'displacement_custom_map', None)
            initial_position = getattr(self, 'displacement_piece_position', [3, 3])
            
            pattern, piece_position, _ = edit_range_pattern(
                initial_pattern, initial_position,
                "Edit Custom Displacement Pattern", self.editor
            )
            
            if pattern is not None:
                self.displacement_custom_map = pattern
                self.displacement_piece_position = piece_position
                # Mark as changed
                if hasattr(self.editor, 'mark_unsaved_changes'):
                    self.editor.mark_unsaved_changes()
                self.log_debug("Displacement pattern updated")
            
        except Exception as e:
            self.log_error(f"Error editing displacement pattern: {e}")
    
    def populate_data(self, data: Dict[str, Any]) -> None:
        """
        Populate the UI widgets with data from an ability.
        
        Args:
            data: Dictionary containing ability data
        """
        try:
            self.log_debug(f"Starting displacement data population with: {data}")
            
            # Displacement direction
            direction_combo = self.get_widget_by_name("displacement_direction")
            if direction_combo and "displaceDirection" in data:
                direction = data["displaceDirection"]
                index = direction_combo.findText(direction)
                if index >= 0:
                    direction_combo.setCurrentIndex(index)
            
            # Displacement distance
            distance_spin = self.get_widget_by_name("displacement_distance")
            if distance_spin and "displaceDistance" in data:
                distance_spin.setValue(data["displaceDistance"])
            
            # Force displacement
            force_check = self.get_widget_by_name("displacement_force")
            if force_check and "displaceForce" in data:
                force_check.setChecked(data["displaceForce"])
            
            # Push through pieces
            push_check = self.get_widget_by_name("displacement_push")
            if push_check and "displacePushThrough" in data:
                push_check.setChecked(data["displacePushThrough"])
            
            # Damage on collision
            damage_check = self.get_widget_by_name("displacement_damage")
            if damage_check and "displaceCollisionDamage" in data:
                damage_check.setChecked(data["displaceCollisionDamage"])
            
            # Custom pattern data
            if "displaceCustomMap" in data:
                self.displacement_custom_map = data["displaceCustomMap"]
            if "displacePiecePosition" in data:
                self.displacement_piece_position = data["displacePiecePosition"]
            
            self.log_debug("Displacement data populated successfully")
            
        except Exception as e:
            self.log_error(f"Error populating data: {e}")
    
    def collect_data(self) -> Dict[str, Any]:
        """
        Collect data from the UI widgets.
        
        Returns:
            Dictionary containing the displacement configuration data
        """
        try:
            data = {}
            
            # Displacement direction
            direction_combo = self.get_widget_by_name("displacement_direction")
            if direction_combo:
                data["displaceDirection"] = direction_combo.currentText()
            
            # Displacement distance
            distance_spin = self.get_widget_by_name("displacement_distance")
            if distance_spin:
                data["displaceDistance"] = distance_spin.value()
            
            # Force displacement
            force_check = self.get_widget_by_name("displacement_force")
            if force_check:
                data["displaceForce"] = force_check.isChecked()
            
            # Push through pieces
            push_check = self.get_widget_by_name("displacement_push")
            if push_check:
                data["displacePushThrough"] = push_check.isChecked()
            
            # Damage on collision
            damage_check = self.get_widget_by_name("displacement_damage")
            if damage_check:
                data["displaceCollisionDamage"] = damage_check.isChecked()
            
            # Custom pattern data
            if hasattr(self, 'displacement_custom_map') and self.displacement_custom_map:
                data["displaceCustomMap"] = self.displacement_custom_map
            if hasattr(self, 'displacement_piece_position'):
                data["displacePiecePosition"] = self.displacement_piece_position
            
            self.log_debug(f"Collected displacement data: {data}")
            return data
            
        except Exception as e:
            self.log_error(f"Error collecting data: {e}")
            return {}
