#!/usr/bin/env python3
"""
Enhanced Caching System for Adventure Chess Creator

This module provides comprehensive caching mechanisms to improve performance
across UI components, validation results, and expensive operations.

Features:
- LRU cache with size limits
- Time-based cache expiration
- Cache statistics and monitoring
- Thread-safe operations
- Memory-efficient storage

Usage:
    from core.performance.caching_system import CacheManager
    
    # Get global cache manager
    cache = CacheManager.get_instance()
    
    # Cache expensive operations
    @cache.cached_method(ttl=300)  # 5 minute TTL
    def expensive_validation(data):
        # Expensive validation logic
        return result
    
    # Manual caching
    cache.set("ui_component_123", widget, ttl=600)
    widget = cache.get("ui_component_123")
"""

import functools
import hashlib
import threading
import time
import weakref
from typing import Any, Dict, Optional, Callable, Union
from collections import OrderedDict


class CacheEntry:
    """Individual cache entry with metadata"""
    
    def __init__(self, value: Any, ttl: Optional[float] = None):
        self.value = value
        self.created_at = time.time()
        self.ttl = ttl
        self.access_count = 0
        self.last_accessed = self.created_at
    
    def is_expired(self) -> bool:
        """Check if cache entry has expired"""
        if self.ttl is None:
            return False
        return time.time() - self.created_at > self.ttl
    
    def access(self) -> Any:
        """Access the cached value and update statistics"""
        self.access_count += 1
        self.last_accessed = time.time()
        return self.value


class LRUCache:
    """Thread-safe LRU cache with TTL support"""
    
    def __init__(self, max_size: int = 1000, default_ttl: Optional[float] = None):
        self.max_size = max_size
        self.default_ttl = default_ttl
        self._cache: OrderedDict[str, CacheEntry] = OrderedDict()
        self._lock = threading.RLock()
        self._stats = {
            'hits': 0,
            'misses': 0,
            'evictions': 0,
            'expired': 0
        }
    
    def get(self, key: str) -> Optional[Any]:
        """Get value from cache"""
        with self._lock:
            if key not in self._cache:
                self._stats['misses'] += 1
                return None
            
            entry = self._cache[key]
            
            # Check if expired
            if entry.is_expired():
                del self._cache[key]
                self._stats['expired'] += 1
                self._stats['misses'] += 1
                return None
            
            # Move to end (most recently used)
            self._cache.move_to_end(key)
            self._stats['hits'] += 1
            
            return entry.access()
    
    def set(self, key: str, value: Any, ttl: Optional[float] = None) -> None:
        """Set value in cache"""
        with self._lock:
            # Use default TTL if not specified
            if ttl is None:
                ttl = self.default_ttl
            
            # Remove existing entry if present
            if key in self._cache:
                del self._cache[key]
            
            # Add new entry
            self._cache[key] = CacheEntry(value, ttl)
            
            # Enforce size limit
            while len(self._cache) > self.max_size:
                oldest_key = next(iter(self._cache))
                del self._cache[oldest_key]
                self._stats['evictions'] += 1
    
    def delete(self, key: str) -> bool:
        """Delete entry from cache"""
        with self._lock:
            if key in self._cache:
                del self._cache[key]
                return True
            return False
    
    def clear(self) -> None:
        """Clear all cache entries"""
        with self._lock:
            self._cache.clear()
    
    def get_stats(self) -> Dict[str, Any]:
        """Get cache statistics"""
        with self._lock:
            total_requests = self._stats['hits'] + self._stats['misses']
            hit_rate = self._stats['hits'] / total_requests if total_requests > 0 else 0
            
            return {
                'size': len(self._cache),
                'max_size': self.max_size,
                'hit_rate': hit_rate,
                **self._stats
            }
    
    def cleanup_expired(self) -> int:
        """Remove expired entries and return count removed"""
        with self._lock:
            expired_keys = [
                key for key, entry in self._cache.items()
                if entry.is_expired()
            ]
            
            for key in expired_keys:
                del self._cache[key]
                self._stats['expired'] += 1
            
            return len(expired_keys)


class CacheManager:
    """Global cache manager with multiple cache categories"""
    
    _instance = None
    _lock = threading.Lock()
    
    def __init__(self):
        self.caches = {
            'ui_components': LRUCache(max_size=500, default_ttl=600),  # 10 minutes
            'validation_results': LRUCache(max_size=1000, default_ttl=300),  # 5 minutes
            'file_operations': LRUCache(max_size=200, default_ttl=1800),  # 30 minutes
            'general': LRUCache(max_size=1000, default_ttl=900),  # 15 minutes
        }
        self._cleanup_thread = None
        self._start_cleanup_thread()
    
    @classmethod
    def get_instance(cls) -> 'CacheManager':
        """Get singleton instance of cache manager"""
        if cls._instance is None:
            with cls._lock:
                if cls._instance is None:
                    cls._instance = cls()
        return cls._instance
    
    def get_cache(self, category: str = 'general') -> LRUCache:
        """Get cache for specific category"""
        return self.caches.get(category, self.caches['general'])
    
    def get(self, key: str, category: str = 'general') -> Optional[Any]:
        """Get value from cache"""
        return self.get_cache(category).get(key)
    
    def set(self, key: str, value: Any, category: str = 'general', ttl: Optional[float] = None) -> None:
        """Set value in cache"""
        self.get_cache(category).set(key, value, ttl)
    
    def delete(self, key: str, category: str = 'general') -> bool:
        """Delete value from cache"""
        return self.get_cache(category).delete(key)
    
    def clear_category(self, category: str) -> None:
        """Clear all entries in a category"""
        if category in self.caches:
            self.caches[category].clear()
    
    def clear_all(self) -> None:
        """Clear all caches"""
        for cache in self.caches.values():
            cache.clear()
    
    def get_all_stats(self) -> Dict[str, Dict[str, Any]]:
        """Get statistics for all caches"""
        return {
            category: cache.get_stats()
            for category, cache in self.caches.items()
        }
    
    def cached_method(self, category: str = 'general', ttl: Optional[float] = None, 
                     key_func: Optional[Callable] = None):
        """Decorator for caching method results"""
        def decorator(func: Callable) -> Callable:
            @functools.wraps(func)
            def wrapper(*args, **kwargs):
                # Generate cache key
                if key_func:
                    cache_key = key_func(*args, **kwargs)
                else:
                    # Default key generation
                    key_parts = [func.__name__]
                    key_parts.extend(str(arg) for arg in args)
                    key_parts.extend(f"{k}={v}" for k, v in sorted(kwargs.items()))
                    cache_key = hashlib.md5("|".join(key_parts).encode()).hexdigest()
                
                # Try to get from cache
                cached_result = self.get(cache_key, category)
                if cached_result is not None:
                    return cached_result
                
                # Execute function and cache result
                result = func(*args, **kwargs)
                self.set(cache_key, result, category, ttl)
                return result
            
            return wrapper
        return decorator
    
    def _start_cleanup_thread(self) -> None:
        """Start background thread for cache cleanup"""
        def cleanup_worker():
            while True:
                time.sleep(60)  # Cleanup every minute
                for cache in self.caches.values():
                    cache.cleanup_expired()
        
        self._cleanup_thread = threading.Thread(target=cleanup_worker, daemon=True)
        self._cleanup_thread.start()


# Global cache manager instance
cache_manager = CacheManager.get_instance()

# Convenience functions
def cached_ui_component(ttl: float = 600):
    """Decorator for caching UI components"""
    return cache_manager.cached_method(category='ui_components', ttl=ttl)

def cached_validation(ttl: float = 300):
    """Decorator for caching validation results"""
    return cache_manager.cached_method(category='validation_results', ttl=ttl)

def cached_file_operation(ttl: float = 1800):
    """Decorator for caching file operations"""
    return cache_manager.cached_method(category='file_operations', ttl=ttl)

def get_cache_stats() -> Dict[str, Dict[str, Any]]:
    """Get statistics for all caches"""
    return cache_manager.get_all_stats()

def clear_all_caches() -> None:
    """Clear all caches"""
    cache_manager.clear_all()
