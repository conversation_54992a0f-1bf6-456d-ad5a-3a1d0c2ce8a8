{"include": ["."], "exclude": ["**/__pycache__", "**/*.pyc", "**/node_modules", ".git", "logs", "data/logs"], "ignore": ["**/tests/**"], "extraPaths": [".", "./schemas", "./utils", "./editors", "./dialogs", "./core", "./tests"], "pythonVersion": "3.13", "pythonPlatform": "Windows", "typeCheckingMode": "basic", "useLibraryCodeForTypes": true, "stubPath": "./typings", "venvPath": ".", "autoImportCompletions": true, "autoSearchPaths": true, "includeFileSpecs": ["**/*.py"], "executionEnvironments": [{"root": ".", "pythonVersion": "3.13", "extraPaths": ["./schemas", "./utils", "./editors", "./dialogs", "./core"]}], "reportUndefinedVariable": "none", "reportGeneralTypeIssues": "none", "reportOptionalMemberAccess": "none", "reportOptionalSubscript": "none", "reportMissingImports": "error", "reportMissingTypeStubs": "none", "reportImportCycles": "warning", "reportUnusedImport": "none", "reportUnusedClass": "none", "reportUnusedFunction": "none", "reportUnusedVariable": "none", "reportDuplicateImport": "warning", "reportWildcardImportFromLibrary": "none", "reportPrivateUsage": "none", "reportConstantRedefinition": "error", "reportIncompatibleMethodOverride": "warning", "reportIncompatibleVariableOverride": "warning", "reportOverlappingOverloads": "error", "reportAttributeAccessIssue": "none", "reportUnknownParameterType": "none", "reportUnknownArgumentType": "none", "reportUnknownLambdaType": "none", "reportUnknownVariableType": "none", "reportUnknownMemberType": "none", "reportMissingParameterType": "none", "reportMissingTypeArgument": "none", "reportInvalidTypeVarUse": "warning", "reportCallInDefaultInitializer": "none", "reportUnnecessaryIsInstance": "none", "reportUnnecessaryCast": "information", "reportUnnecessaryComparison": "none", "reportUnnecessaryContains": "information", "reportAssertAlwaysTrue": "none", "reportSelfClsParameterName": "warning", "reportImplicitStringConcatenation": "none", "reportUnnecessaryTypeIgnoreComment": "none", "reportMatchNotExhaustive": "none", "reportShadowedImports": "none", "reportUninitializedInstanceVariable": "none", "reportImplicitOverride": "none", "reportPropertyTypeMismatch": "none", "reportFunctionMemberAccess": "none", "reportInvalidStubStatement": "none", "reportIncompleteStub": "none", "reportUnsupportedDunderAll": "none", "reportUnusedCoroutine": "warning", "reportArgumentType": "none", "reportReturnType": "none", "reportOperatorIssue": "none", "reportIndexIssue": "none", "reportInvalidTypeForm": "none", "reportUnreachable": "none", "reportCallIssue": "none"}